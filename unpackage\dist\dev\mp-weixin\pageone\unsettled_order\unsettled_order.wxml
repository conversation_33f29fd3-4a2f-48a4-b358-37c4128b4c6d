<view class="main data-v-334c4ad9"><navigator url="../records_consumption/records_consumption" hover-class="none" class="data-v-334c4ad9"><van-notice-bar vue-id="363d914d-1" scrollable="{{true}}" left-icon="volume-o" color="#FFFFFF" speed="40" data-com-type="wx" class="data-v-334c4ad9" bind:__l="__l" vue-slots="{{['default']}}">因网络原因无法打开柜门,请前往个人中心-查看更多-消费记录,查看开柜密码打开柜门</van-notice-bar></navigator><view class="hand data-v-334c4ad9"><van-pull-refresh bind:refresh="__e" bind:input="__e" vue-id="363d914d-2" value="{{refreshing}}" data-event-opts="{{[['^refresh',[['onRefresh']]],['^input',[['__set_model',['','refreshing','$event',[]]]]]]}}" class="data-v-334c4ad9" bind:__l="__l" vue-slots="{{['default']}}"><van-list vue-id="{{('363d914d-3')+','+('363d914d-2')}}" finished="{{finished}}" finished-text="没有更多了" value="{{loading}}" data-event-opts="{{[['^load',[['loadMore']]],['^input',[['__set_model',['','loading','$event',[]]]]]]}}" bind:load="__e" bind:input="__e" class="data-v-334c4ad9" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{showlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><van-cell bind:click="__e" vue-id="{{('363d914d-4-'+index)+','+('363d914d-3')}}" data-event-opts="{{[['^click',[['onDetail',['$0','$1','$2'],[[['showlist','',index,'id']],[['showlist','',index,'deviceId']],[['showlist','',index,'isCharge']]]]]]]}}" data-com-type="wx" class="data-v-334c4ad9" bind:__l="__l" vue-slots="{{['default']}}"><view class="show data-v-334c4ad9"><view class="showhand data-v-334c4ad9"><view class="hleft data-v-334c4ad9"><label class="_span data-v-334c4ad9">{{"充电柜编号："+item.code}}</label></view><view hidden="{{!(item.isCharge==0)}}" class="hright data-v-334c4ad9">充电中</view><view hidden="{{!(item.isCharge==1)}}" class="hright data-v-334c4ad9">未结算</view></view><view class="showfoot data-v-334c4ad9"><view class="fleft data-v-334c4ad9">{{'订单编号：'+item.orderNum+''}}</view><view class="fleft data-v-334c4ad9">{{'小区名称：'+item.projectName+''}}</view><view class="fleft data-v-334c4ad9">{{'开始时间：'+item.startTime+''}}</view><view class="fleft data-v-334c4ad9">{{'开柜密码：'+item.password+''}}</view></view></view></van-cell></block></van-list></van-pull-refresh></view></view>