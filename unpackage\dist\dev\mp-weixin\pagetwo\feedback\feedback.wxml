<view class="main data-v-135dca7f"><view class="show data-v-135dca7f"><van-field vue-id="585c0667-1" autosize="{{true}}" type="textarea" maxlength="200" placeholder="请输入你反馈的内容" value="{{message}}" data-event-opts="{{[['^change',[['txtchange']]],['^input',[['__set_model',['','message','$event',[]]]]]]}}" data-com-type="wx" bind:change="__e" bind:input="__e" class="data-v-135dca7f" bind:__l="__l"></van-field><view data-event-opts="{{[['tap',[['gobtn',['$event']]]]]}}" class="sbtn data-v-135dca7f" bindtap="__e">提交内容</view></view><van-toast vue-id="585c0667-2" id="van-toast" data-com-type="wx" class="data-v-135dca7f" bind:__l="__l"></van-toast><van-dialog vue-id="585c0667-3" id="van-dialog" data-com-type="wx" class="data-v-135dca7f" bind:__l="__l"></van-dialog></view>