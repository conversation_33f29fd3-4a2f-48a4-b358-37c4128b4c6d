
page{
	height: 100%;
	background-color:#242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-d5606cc2 {
  width: 100%;
  overflow-x: hidden;
  background-color: #242225;
}
.main .show.data-v-d5606cc2 {
  width: 100%;
  margin-top: 85rpx;
  background-color: #242225;
  box-sizing: border-box;
  padding: 0 50rpx;
  padding-bottom: 50rpx;
}
.main .show .shand.data-v-d5606cc2 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  padding: 29rpx 0;
  padding-left: 36rpx;
  background: #343136;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
}
.main .show .shand .shandleft.data-v-d5606cc2 {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
}
.main .show .shand .shandleft1.data-v-d5606cc2 {
  font-size: 24rpx;
  font-family: PingFang SC Regular, PingFang SC Regular-Regular;
  font-weight: 400;
  color: #333333;
}
.main .show .shand .shandright.data-v-d5606cc2 {
  font-size: 28rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #fff;
}
.main .show .shand .shandright1.data-v-d5606cc2 {
  font-size: 32rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #FFA02E;
}
.main .show .ktwo.data-v-d5606cc2 {
  width: 100%;
  box-sizing: border-box;
}
.main .show .ktwo.data-v-d5606cc2 .van-cell:after {
  border-bottom: 0rpx;
  border-bottom-color: transparent;
}
.main .show .ktwo.data-v-d5606cc2 .van-cell__title {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
}
.main .show .ktwo.data-v-d5606cc2 .van-collapse-item {
  background-color: #343136;
  border-radius: 24rpx;
}
.main .show .ktwo.data-v-d5606cc2 .van-cell {
  padding: 29rpx 36rpx;
  background: #343136;
  border-radius: 24rpx;
}
.main .show .ktwo.data-v-d5606cc2 .van-cell:not(:last-child)::after {
  border-bottom: 0rpx;
}
.main .show .ktwo.data-v-d5606cc2 .van-radio__label {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  margin-left: 90rpx;
}
.main .show .ktwo.data-v-d5606cc2 .van-radio__icon {
  margin-left: 80rpx;
}
.main .show .ktwo.data-v-d5606cc2 .van-collapse-item__content {
  background-color: #343136;
  border-radius: 24rpx;
}
.main .show .ktwo.data-v-d5606cc2 .van-hairline--top-bottom::after {
  border-width: 0rpx;
}
.main .show .ktwo.data-v-d5606cc2 .van-radio {
  height: 70rpx;
  align-items: center;
}
.main .show .ktwo.data-v-d5606cc2 .van-collapse-item__wrapper {
  margin-bottom: 10rpx;
}
.main .show .ktwo.data-v-d5606cc2 .van-cell:not(:last-child)::after {
  border-bottom: 0rpx;
}
.main .show .ktwo.data-v-d5606cc2 .van-radio__icon--checked {
  background-color: #EC651C;
  border-color: #EC651C;
}
.main .show .shthree.data-v-d5606cc2 {
  width: 100%;
  box-sizing: border-box;
  padding: 26rpx 0;
  padding-left: 19rpx;
}
.main .show .shthree .shthreeleft.data-v-d5606cc2 {
  font-size: 24rpx;
  font-family: PingFang SC Regular, PingFang SC Regular-Regular;
  font-weight: 400;
  color: #333333;
}
.main .show .shthree .kthreefoot.data-v-d5606cc2 {
  width: 100%;
  box-sizing: border-box;
  margin-top: 21rpx;
}
.main .show .shthree .kthreefoot.data-v-d5606cc2 .van-cell {
  background-color: #F0F0F0;
  height: 236rpx;
  border-radius: 16rpx;
}
.main .show .shfour.data-v-d5606cc2 {
  width: 100%;
  box-sizing: border-box;
  margin-top: 40rpx;
}
.main .show .shfour .shfourleft.data-v-d5606cc2 {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  font-size: 32rpx;
  font-family: PingFang SC Regular, PingFang SC Regular-Regular;
  font-weight: 400;
  text-align: center;
  line-height: 88rpx;
  color: #ffffff;
  margin-bottom: 40rpx;
}
.main .show .shfour .shfourright.data-v-d5606cc2 {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  background: #343136;
  font-size: 32rpx;
  font-family: PingFang SC Regular, PingFang SC Regular-Regular;
  font-weight: 400;
  text-align: center;
  line-height: 88rpx;
  color: #D0D0D0;
}
.main .show .shfive.data-v-d5606cc2 {
  margin-top: 59rpx;
}
.main .show .shfive .shfivehand.data-v-d5606cc2 {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
.main .show .shfive .shfivefoot.data-v-d5606cc2 {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
  line-height: 50rpx;
}
.main .footdibu.data-v-d5606cc2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.main .footdibu .fdbfive.data-v-d5606cc2 {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 94rpx;
  height: 46rpx;
}
.main .footdibu .fdbfive image.data-v-d5606cc2 {
  width: 94rpx;
  height: 46rpx;
}
.main .footdibu .fdbone.data-v-d5606cc2 {
  position: absolute;
  width: 260rpx;
  height: 80rpx;
  top: 602rpx;
  left: 40rpx;
  border-radius: 50rpx;
  box-shadow: rgba(0, 0, 0, 0.6) 0 0 0 100vh;
}
.main .footdibu .fdbtwo.data-v-d5606cc2 {
  position: absolute;
  width: 126rpx;
  height: 132rpx;
  top: 730rpx;
  left: 250rpx;
}
.main .footdibu .fdbtwo image.data-v-d5606cc2 {
  width: 126rpx;
  height: 132rpx;
}
.main .footdibu .fdbthree.data-v-d5606cc2 {
  position: absolute;
  width: 450rpx;
  top: 899rpx;
  text-align: center;
  left: 142rpx;
  color: #FFFFFF;
}
.main .footdibu .fdbthree .fdbthreeshang.data-v-d5606cc2 {
  font-size: 39rpx;
  font-family: YouYuan;
  font-weight: 700;
}
.main .footdibu .fdbthree .fdbthreezhong.data-v-d5606cc2 {
  margin-top: 38rpx;
  font-size: 21rpx;
  font-family: YouYuan;
  font-weight: 400;
}
.main .footdibu .fdbfour.data-v-d5606cc2 {
  position: absolute;
  top: 1003rpx;
  left: 262rpx;
  width: 198rpx;
  height: 76rpx;
}
.main .footdibu .fdbfour image.data-v-d5606cc2 {
  width: 198rpx;
  height: 76rpx;
}
.main .footdibu .fdbsix.data-v-d5606cc2 {
  position: absolute;
  top: 1120rpx;
  left: 272rpx;
  font-size: 23rpx;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #FFFFFF;
}
.main .footdibu .fdbsix.data-v-d5606cc2 .van-checkbox__label {
  color: #FFFFFF;
}
.main .footdibu .fdbsix.data-v-d5606cc2 .van-checkbox__icon--checked .van-icon {
  border-color: #FFFFFF;
  background-color: transparent;
}
.main .footdibu1.data-v-d5606cc2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.main .footdibu1 .fdbfive.data-v-d5606cc2 {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 94rpx;
  height: 46rpx;
}
.main .footdibu1 .fdbfive image.data-v-d5606cc2 {
  width: 94rpx;
  height: 46rpx;
}
.main .footdibu1 .fdbone.data-v-d5606cc2 {
  position: absolute;
  width: 260rpx;
  height: 80rpx;
  top: 516rpx;
  left: 40rpx;
  border-radius: 50rpx;
  box-shadow: rgba(0, 0, 0, 0.6) 0 0 0 100vh;
}
.main .footdibu1 .fdbtwo.data-v-d5606cc2 {
  position: absolute;
  width: 126rpx;
  height: 132rpx;
  top: 644rpx;
  left: 250rpx;
}
.main .footdibu1 .fdbtwo image.data-v-d5606cc2 {
  width: 126rpx;
  height: 132rpx;
}
.main .footdibu1 .fdbthree.data-v-d5606cc2 {
  position: absolute;
  width: 450rpx;
  top: 813rpx;
  text-align: center;
  left: 142rpx;
  color: #FFFFFF;
}
.main .footdibu1 .fdbthree .fdbthreeshang.data-v-d5606cc2 {
  font-size: 39rpx;
  font-family: YouYuan;
  font-weight: 700;
}
.main .footdibu1 .fdbthree .fdbthreezhong.data-v-d5606cc2 {
  margin-top: 38rpx;
  font-size: 21rpx;
  font-family: YouYuan;
  font-weight: 400;
}
.main .footdibu1 .fdbfour.data-v-d5606cc2 {
  position: absolute;
  top: 917rpx;
  left: 262rpx;
  width: 198rpx;
  height: 76rpx;
}
.main .footdibu1 .fdbfour image.data-v-d5606cc2 {
  width: 198rpx;
  height: 76rpx;
}
.main .footdibu1 .fdbsix.data-v-d5606cc2 {
  position: absolute;
  top: 1030rpx;
  left: 272rpx;
  font-size: 23rpx;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #FFFFFF;
}
.main .footdibu1 .fdbsix.data-v-d5606cc2 .van-checkbox__label {
  color: #FFFFFF;
}
.main .footdibu1 .fdbsix.data-v-d5606cc2 .van-checkbox__icon--checked .van-icon {
  border-color: #FFFFFF;
  background-color: transparent;
}

