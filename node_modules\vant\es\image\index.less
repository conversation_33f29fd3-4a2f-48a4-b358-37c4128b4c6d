@import '../style/var';

.van-image {
  position: relative;
  display: inline-block;

  &--round {
    overflow: hidden;
    border-radius: 50%;

    img {
      border-radius: inherit;
    }
  }

  &__img,
  &__error,
  &__loading {
    display: block;
    width: 100%;
    height: 100%;
  }

  &__error,
  &__loading {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: @image-placeholder-text-color;
    font-size: @image-placeholder-font-size;
    background-color: @image-placeholder-background-color;
  }

  &__loading-icon {
    color: @image-loading-icon-color;
    font-size: @image-loading-icon-size;
  }

  &__error-icon {
    color: @image-error-icon-color;
    font-size: @image-error-icon-size;
  }
}
