import { get, isFunction } from '..';
import { camelize } from '../format/string';
import locale from '../../locale';
export function createI18N(name) {
  var prefix = camelize(name) + '.';
  return function (path) {
    var messages = locale.messages();
    var message = get(messages, prefix + path) || get(messages, path);

    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }

    return isFunction(message) ? message.apply(void 0, args) : message;
  };
}