<view class="main data-v-40ddfa5a"><block wx:if="{{this.wechatLogo}}"><view data-event-opts="{{[['tap',[['gzbtn',['$event']]]]]}}" class="foothand data-v-40ddfa5a" bindtap="__e">点击关注公众号，接收充电消息通知</view></block><tou_hand vue-id="4a3c5c28-1" data-ref="childComponent" class="data-v-40ddfa5a vue-ref" bind:__l="__l"></tou_hand><navigator url="../../pagetwo/records_consumption/records_consumption" hover-class="none" class="data-v-40ddfa5a"><van-notice-bar vue-id="4a3c5c28-2" scrollable="{{true}}" left-icon="volume-o" color="#FFFFFF" speed="40" data-com-type="wx" class="data-v-40ddfa5a" bind:__l="__l" vue-slots="{{['default']}}">亲爱的充电柜用户，因网络原因无法打开柜门,请前往个人中心-查看更多-消费记录,查看开柜密码打开柜门</van-notice-bar></navigator><block wx:if="{{$root.g0!=0}}"><view class="hand data-v-40ddfa5a"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onDetail',['$0','$1','$2'],[[['showlist','',index,'id']],[['showlist','',index,'deviceId']],[['showlist','',index,'spareVar5']]]]]]]}}" class="show data-v-40ddfa5a" bindtap="__e"><view class="showhand data-v-40ddfa5a"><view class="hleft data-v-40ddfa5a"><block wx:if="{{item.g1!=9}}"><label class="_span data-v-40ddfa5a">充电桩编号<label style="color:#007AFF;" class="_span data-v-40ddfa5a">（桩）</label>{{"："+item.$orig.code}}</label></block><block wx:if="{{item.g2==9}}"><label class="_span data-v-40ddfa5a">充电柜编号<label style="color:#07C160;" class="_span data-v-40ddfa5a">（柜）</label>{{"："+item.$orig.code}}</label></block></view><view class="hright data-v-40ddfa5a">充电中</view></view><view class="showfoot data-v-40ddfa5a"><view class="fleft data-v-40ddfa5a">{{'订单编号：'+item.$orig.orderNum+''}}</view><view class="fleft data-v-40ddfa5a">{{'小区名称：'+item.$orig.projectName+''}}</view><view class="fleft data-v-40ddfa5a">{{'开始时间：'+item.$orig.startTime+''}}</view><block wx:if="{{item.$orig.spareVar5==1}}"><view class="fleft data-v-40ddfa5a" style="display:flex;align-items:center;">充电守护：<view style="color:#07C160;" class="data-v-40ddfa5a">生效中</view><view data-event-opts="{{[['tap',[['xysm',['$event']]]]]}}" style="color:#FFA02E;margin-left:20rpx;" bindtap="__e" class="data-v-40ddfa5a">《协议说明》</view><block wx:if="{{White==1}}"><view data-event-opts="{{[['tap',[['lxkf',['$0'],[[['showlist','',index,'orderNum']]]]]]]}}" class="fleftson data-v-40ddfa5a" bindtap="__e">客服</view></block></view></block></view></view></block><uni-load-more vue-id="4a3c5c28-3" status="{{status}}" icon-size="{{16}}" content-text="{{contentText}}" class="data-v-40ddfa5a" bind:__l="__l"></uni-load-more></view></block><block wx:if="{{$root.g3==0}}"><view class="notap data-v-40ddfa5a"><image src="../../static/tabBarimg/zwdd.png" class="data-v-40ddfa5a"></image>暂无订单</view></block><van-popup vue-id="4a3c5c28-4" show="{{gzshow}}" data-com-type="wx" class="data-v-40ddfa5a" bind:__l="__l" vue-slots="{{['default']}}"><view class="gztan data-v-40ddfa5a"><view class="gztanimg data-v-40ddfa5a"><image src="{{wechaturl}}" class="_img data-v-40ddfa5a"></image></view><view class="gztantxt data-v-40ddfa5a">长按识别二维码，关注公众号，接收充电消息通知</view></view></van-popup><tab-bar vue-id="4a3c5c28-5" tab01="3" class="data-v-40ddfa5a" bind:__l="__l"></tab-bar><view class="kf data-v-40ddfa5a"><van-popup vue-id="4a3c5c28-6" show="{{kfshow}}" data-com-type="wx" class="data-v-40ddfa5a" bind:__l="__l" vue-slots="{{['default']}}"><view class="kfmap data-v-40ddfa5a"><view data-event-opts="{{[['tap',[['gkf',['$event']]]]]}}" class="one data-v-40ddfa5a" bindtap="__e"><image src="../../static/tabBarimg/kfclose.png" class="data-v-40ddfa5a"></image></view><view class="two data-v-40ddfa5a">专属客服</view><view class="three data-v-40ddfa5a">您可以通过以下方式联系到我哦</view><view class="four data-v-40ddfa5a"><block wx:if="{{xylist.phoneStatuss==1}}"><view data-event-opts="{{[['tap',[['callphone',['$event']]]]]}}" class="fourfirst data-v-40ddfa5a" bindtap="__e"><image src="../../static/tabBarimg/kfphone.png" class="data-v-40ddfa5a"></image>电话客服</view></block><block wx:if="{{xylist.imgStatuss==1&&xylist.phoneStatuss==1}}"><view class="fourthired data-v-40ddfa5a"></view></block><block wx:if="{{xylist.imgStatuss==1}}"><view data-event-opts="{{[['tap',[['zaixian',['$event']]]]]}}" class="foursecond data-v-40ddfa5a" bindtap="__e"><image src="../../static/tabBarimg/kfwx.png" class="data-v-40ddfa5a"></image>在线客服</view></block></view></view></van-popup><van-popup vue-id="4a3c5c28-7" show="{{wxtcshow}}" data-com-type="wx" class="data-v-40ddfa5a" bind:__l="__l" vue-slots="{{['default']}}"><view class="kfmap data-v-40ddfa5a"><view data-event-opts="{{[['tap',[['gwx',['$event']]]]]}}" class="one data-v-40ddfa5a" bindtap="__e"><image src="../../static/tabBarimg/kfclose.png" class="data-v-40ddfa5a"></image></view><view class="five data-v-40ddfa5a"><image src="{{xylist.wechatImg}}" show-menu-by-longpress="true" class="data-v-40ddfa5a"></image></view><view class="six data-v-40ddfa5a"><view class="data-v-40ddfa5a">关注公众号</view><view class="data-v-40ddfa5a">即可享受专属客服在线咨询服务</view></view></view></van-popup></view><van-toast vue-id="4a3c5c28-8" id="van-toast" data-com-type="wx" class="data-v-40ddfa5a" bind:__l="__l"></van-toast><van-dialog vue-id="4a3c5c28-9" id="van-dialog" data-com-type="wx" class="data-v-40ddfa5a" bind:__l="__l"></van-dialog></view>