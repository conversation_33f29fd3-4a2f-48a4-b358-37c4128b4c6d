
page{
	height: 100%;
	background-color:#242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-d0502ec2 {
  width: 100%;
}
.main.data-v-d0502ec2 .van-list__finished-text {
  margin-top: 100rpx;
  padding-bottom: 200rpx;
}
.main.data-v-d0502ec2 .van-hairline--top-bottom::after {
  border-width: 0;
}
.main.data-v-d0502ec2 .van-collapse-item__content {
  padding: 0 30rpx;
  background-color: #343136;
  border-radius: 0rpx 0rpx 24rpx 24rpx;
}
.main.data-v-d0502ec2 .van-cell:after {
  border-bottom: 0rpx;
  border-bottom-color: transparent;
}
.main.data-v-d0502ec2 .van-collapse-item {
  border-radius: 24rpx !important;
  background-color: #343136 !important;
  margin-top: 30rpx;
}
.main.data-v-d0502ec2 .van-cell {
  background-color: transparent;
  align-items: center;
  padding: 30rpx 40rpx !important;
}
.main.data-v-d0502ec2 .van-hairline--top-bottom::after {
  border-width: 0;
}
.main.data-v-d0502ec2 .van-hairline--top:after {
  border-top-width: 0rpx;
}
.main.data-v-d0502ec2 .van-tab {
  font-size: 32rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #999999;
}
.main.data-v-d0502ec2 .van-tab--active {
  font-size: 32rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #FFA02E;
}
.main.data-v-d0502ec2 .van-tabs__nav {
  background-color: #242225;
}
.main.data-v-d0502ec2 .van-tabs__line {
  width: 91rpx !important;
  height: 4rpx;
  border-radius: 2px;
  background-color: #FFA02E;
}
.main .show.data-v-d0502ec2 {
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx;
}
.main .show .one.data-v-d0502ec2 {
  box-sizing: border-box;
  border-radius: 24rpx !important;
  background-color: #343136 !important;
  margin-top: 30rpx;
  padding: 30rpx 40rpx !important;
}
.main .show .one .onehand.data-v-d0502ec2 {
  font-size: 26rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #fff;
  margin-bottom: 15rpx;
  border-bottom: 1px dashed #e5e5e5;
}
.main .show .one .onehand .onetime.data-v-d0502ec2 {
  font-size: 20rpx;
  font-family: PingFang SC Regular, PingFang SC Regular-Regular;
  font-weight: 400;
  text-align: right;
  color: #D0D0D0;
  line-height: 36px;
}
.main .show .one .onetxt.data-v-d0502ec2 {
  font-size: 24rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 500;
  color: #D0D0D0;
  line-height: 40rpx;
}
.main .show .one .onetxt .onetxtone.data-v-d0502ec2 {
  font-size: 24rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #fff;
}
.main .showtwo.data-v-d0502ec2 {
  box-sizing: border-box;
  padding: 20rpx;
}
.main .showtwo .bone .boneshang.data-v-d0502ec2 {
  color: #fff;
  font-size: 30rpx;
}
.main .showtwo .bone .bonexia.data-v-d0502ec2 {
  color: #c5c5c5;
  font-size: 22rpx;
}
.main .showtwo .btwo .btwotxt.data-v-d0502ec2 {
  line-height: 40rpx;
}
.main .showtwo .btwo .btwoimg.data-v-d0502ec2 {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}
.main .showtwo .btwo .btwoimg image.data-v-d0502ec2 {
  border-radius: 20rpx;
  width: 150rpx;
  height: 150rpx;
}

