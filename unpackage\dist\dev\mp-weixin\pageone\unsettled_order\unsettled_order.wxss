@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-334c4ad9 {
  width: 100%;
  overflow-x: hidden;
  margin-bottom: 140rpx;
}
.main.data-v-334c4ad9 .van-list__finished-text {
  margin-top: 100rpx;
  padding-bottom: 200rpx;
}
.main.data-v-334c4ad9 .van-hairline--top-bottom::after {
  border-width: 0;
}
.main.data-v-334c4ad9 .van-cell {
  padding: 0rpx;
  width: 650rpx;
  height: 344rpx;
  background: #343136;
  border-radius: 24rpx;
  margin: 0 auto;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}
.main.data-v-334c4ad9 .van-cell:not(:last-child)::after {
  border-bottom: 0rpx;
}
.main.data-v-334c4ad9 .van-notice-bar {
  width: 650rpx;
  height: 70rpx;
  background: #343136;
  border-radius: 24rpx;
  padding: 0 24rpx;
  margin: 0 auto;
  margin-top: 30rpx;
}
.main .hand.data-v-334c4ad9 {
  width: 100%;
  margin-top: 90rpx;
}
.main .show.data-v-334c4ad9 {
  margin-top: 85rpx;
  margin: 0 auto;
  box-sizing: border-box;
}
.main .show .showhand.data-v-334c4ad9 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  padding: 30rpx 26rpx;
  background-color: #3C393E;
}
.main .show .showhand .hleft.data-v-334c4ad9 {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
.main .show .showhand .hright.data-v-334c4ad9 {
  font-size: 26rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFA02E;
}
.main .show .showfoot.data-v-334c4ad9 {
  width: 100%;
  box-sizing: border-box;
  padding: 13rpx 0rpx 24rpx 26rpx;
}
.main .show .showfoot .fleft.data-v-334c4ad9 {
  font-size: 26rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
  margin-bottom: 10rpx;
}

