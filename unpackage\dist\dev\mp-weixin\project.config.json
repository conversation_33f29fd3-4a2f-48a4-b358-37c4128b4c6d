{"description": "项目配置文件。", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "postcss": false, "minified": false, "newFeature": true, "bigPackageSizeSupport": true, "useCompilerModule": false, "userConfirmedUseCompilerModuleSwitch": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "libVersion": "2.32.3", "appid": "wx361c693866e9672c", "projectname": "66_Chargingpile_applet", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}, "cloudfunctionRoot": "cloudfunctions/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}