<wxs src="../wxs/utils.wxs" module="utils" />

<view class="van-collapse-item custom-class {{ index !== 0 ? 'van-hairline--top' : '' }}">
  <van-cell
    title="{{ title }}"
    title-class="title-class"
    icon="{{ icon }}"
    value="{{ value }}"
    label="{{ label }}"
    is-link="{{ isLink }}"
    clickable="{{ clickable }}"
    border="{{ border && expanded }}"
    class="{{ utils.bem('collapse-item__title', { disabled, expanded }) }}"
    right-icon-class="van-cell__right-icon"
    custom-class="van-cell"
    hover-class="van-cell--hover"
    bind:click="onClick"
  >
    <slot
      name="title"
      slot="title"
    />
    <slot
      name="icon"
      slot="icon"
    />
    <slot name="value" />
    <slot
      name="right-icon"
      slot="right-icon"
    />
  </van-cell>
  <view
    class="{{ utils.bem('collapse-item__wrapper') }}"
    style="height: 0;"
    animation="{{ animation }}"
  >
    <view
      class="van-collapse-item__content content-class"
    >
      <slot />
    </view>
  </view>
</view>
