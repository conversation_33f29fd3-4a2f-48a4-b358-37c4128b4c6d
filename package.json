{"name": "66_Chargingpile_applet", "version": "1.0.0", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build"}, "author": "", "license": "ISC", "dependencies": {"babel-polyfill": "^6.26.0", "jweixin-module": "^1.6.0", "qrcode-decoder": "^0.1.2", "vant": "^2.10.3"}, "description": "", "devDependencies": {"cross-env": "^7.0.3", "webpack-bundle-analyzer": "^4.4.2"}}