@import '../style/var';

.van-password-input {
  position: relative;
  margin: @password-input-margin;
  user-select: none;

  &__info,
  &__error-info {
    margin-top: @padding-md;
    font-size: @password-input-info-font-size;
    text-align: center;
  }

  &__info {
    color: @password-input-info-color;
  }

  &__error-info {
    color: @password-input-error-info-color;
  }

  &__security {
    display: flex;
    width: 100%;
    height: @password-input-height;
    cursor: pointer;

    &::after {
      border-radius: @password-input-border-radius;
    }

    li {
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: @password-input-font-size;
      line-height: 1.2;
      background-color: @password-input-background-color;
    }

    i {
      position: absolute;
      top: 50%;
      left: 50%;
      width: @password-input-dot-size;
      height: @password-input-dot-size;
      background-color: @password-input-dot-color;
      border-radius: 100%;
      transform: translate(-50%, -50%);
      visibility: hidden;
    }
  }

  &__cursor {
    position: absolute;
    top: 50%;
    left: 50%;
    width: @number-keyboard-cursor-width;
    height: @number-keyboard-cursor-height;
    background-color: @number-keyboard-cursor-color;
    transform: translate(-50%, -50%);
    animation: @number-keyboard-cursor-animation-duration van-cursor-flicker
      infinite;
  }
}

@keyframes van-cursor-flicker {
  from {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
