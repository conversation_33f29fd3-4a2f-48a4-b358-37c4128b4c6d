{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "66_Chargingpile_applet", "setting": {"compileHotReLoad": true}, "condition": {"miniprogram": {"list": [{"name": "", "pathName": "pages/index/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pagetwo/refund_details/refund_details", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pagetwo/recharge_record/recharge_record", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pagetwo/records_consumption/records_consumption", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pagetwo/refund/refund", "query": "type=2", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pageone/poweron/poweron", "query": "device=14104905&port=00&cmd=1&from=3&yc=1", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pagetwo/apply_refund/apply_refund", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pagetwo/guard_record/guard_record", "query": "", "launchMode": "default", "scene": null}]}}}