<view class="content reg-page data-v-b237504c"><view class="form-area data-v-b237504c"><view class="operate-btn reg-page-btn data-v-b237504c"><block wx:if="{{isGetMobile&&!showPhoneAuthBtn}}"><button class="round bg-my-blue data-v-b237504c" type="primary" open-type="{{isXieyi?'getPhoneNumber':''}}" disabled="{{submitToggle}}" loading="{{submitToggle}}" data-event-opts="{{[['getphonenumber',[['onGetPhoneNumber',['$event']]]],['tap',[['handleOneKeyLogin',['$event']]]]]}}" bindgetphonenumber="__e" bindtap="__e">{{''+(submitToggle?'登录中...':'一键登录')+''}}</button></block><block wx:if="{{isGetMobile&&showPhoneAuthBtn}}"><button class="round bg-my-blue data-v-b237504c" type="primary" open-type="getPhoneNumber" disabled="{{submitToggle}}" loading="{{submitToggle}}" data-event-opts="{{[['getphonenumber',[['onGetPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">{{''+(submitToggle?'登录中...':'授权手机号登录')+''}}</button></block><block wx:if="{{!isGetMobile}}"><button class="round bg-my-blue data-v-b237504c" type="primary" disabled="{{submitToggle}}" loading="{{submitToggle}}" data-event-opts="{{[['tap',[['login']]]]}}" bindtap="__e">{{''+(submitToggle?'登录中...':'一键登录')+''}}</button></block></view></view><view data-event-opts="{{[['tap',[['handleOpenPrivacyContract']]]]}}" class="flex-aic-jcc-wrap reset data-v-b237504c" bindtap="__e"><label data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="flex-aic data-v-b237504c" catchtap="__e"><checkbox checked="{{isXieyi}}" class="data-v-b237504c"></checkbox></label><text class="data-v-b237504c">我已经详细阅读并同意</text><text data-event-opts="{{[['tap',[['handleOpenPrivacyContract']]]]}}" class="text-my-blue data-v-b237504c" bindtap="__e">《隐私政策》</text></view><van-toast vue-id="35a7246c-1" id="van-toast" data-com-type="wx" class="data-v-b237504c" bind:__l="__l"></van-toast><van-dialog vue-id="35a7246c-2" id="van-dialog" data-com-type="wx" class="data-v-b237504c" bind:__l="__l"></van-dialog><block wx:if="{{showFollowGuide}}"><view class="popup-overlay data-v-b237504c"><view class="follow-guide-popup data-v-b237504c"><view class="popup-header data-v-b237504c"><text class="popup-title data-v-b237504c">充电提示</text></view><view class="popup-content data-v-b237504c"><text class="main-tip data-v-b237504c">请扫描设备二维码进行充电</text></view><block wx:if="{{qrCodeUrl}}"><view class="qr-container data-v-b237504c"><view class="qr-section data-v-b237504c"><image class="qr-code data-v-b237504c" src="{{qrCodeUrl}}" mode="aspectFit" show-menu-by-longpress="true"></image><text class="qr-tip data-v-b237504c">长按识别二维码，关注公众号，在公众号内完成手机号绑定</text></view><view class="important-notice data-v-b237504c"><view class="notice-header data-v-b237504c"><text class="notice-icon data-v-b237504c">💡</text><text class="notice-title data-v-b237504c">重要提示：</text></view><view class="notice-list data-v-b237504c"><view class="notice-item data-v-b237504c"><text class="bullet data-v-b237504c">•</text><text class="notice-text data-v-b237504c">手机号绑定必须在公众号内完成</text></view><view class="notice-item data-v-b237504c"><text class="bullet data-v-b237504c">•</text><text class="notice-text data-v-b237504c">已关注用户需要先在公众号内充电一次</text></view><view class="notice-item data-v-b237504c"><text class="bullet data-v-b237504c">•</text><text class="notice-text data-v-b237504c">完成绑定后，扫码充电将更加便捷</text></view></view></view></view></block></view></view></block></view>