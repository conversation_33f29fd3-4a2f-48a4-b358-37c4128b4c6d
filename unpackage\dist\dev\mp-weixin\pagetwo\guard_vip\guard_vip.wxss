
page {
	height: 100%;
	background-color: #242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-19409da7 {
  width: 100%;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: hidden;
  background-color: #242225;
}
.main .hand.data-v-19409da7 {
  text-size-adjust: 100% !important;
  -webkit-text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
  width: 100%;
  background-color: #242225;
  font-size: 30rpx;
  padding: 20rpx 0;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
  color: #ffffff;
  position: relative;
}
.main .hand image.data-v-19409da7 {
  width: 22rpx;
  height: 39rpx;
  position: absolute;
  left: 31rpx;
  top: 22rpx;
}
.main .show.data-v-19409da7 {
  width: 100%;
  height: 100%;
  overflow-y: hidden;
  position: relative;
}
.main .show .one.data-v-19409da7 {
  width: 690rpx;
  height: 392rpx;
  background: linear-gradient(180deg, #FFE7BF 0%, #FDC894 100%);
  box-shadow: inset -1rpx 2rpx 5rpx 1rpx rgba(255, 255, 255, 0.79);
  border-radius: 20rpx;
  margin: 0 auto;
  margin-top: 60rpx;
  box-sizing: border-box;
  padding: 31rpx 25rpx 0 54rpx;
}
.main .show .one .onefirst.data-v-19409da7 {
  width: 172rpx;
  height: 47rpx;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKwAAAAvCAMAAABJy9GcAAAAY1BMVEUAAADJfibJeyTKfSTIfSXIfSXIfibIfiO9cBfIfiXIfiXIfSTIfiXDdhvEeB7IfSTIfiXIfSTHeiDIfiXIfSXIfSPHeyHGfCDGfSHIfSXIfiTIfSTIfSPIfiXIfSTIfSTIfia+/cvUAAAAIHRSTlMAXzA/p/vfQQfY7671DROAx5Ub0MBLKSQ16YhwVuO5eIJW5O8AAAKHSURBVFjD1djbcoIwFIXhBRJMQoACchBQ9/s/ZWkCtZRDcTRT+S4cL7z4Z5shWxFoNYww0BI05k0ROSMlOg29HmuwAWkfMCJGXwQ80o4ujfghOi2j1/PCh2PVauzwQdd5tZuiLH11LCWwo/JJ5o/FhvKvWObAjuhELOavjSVVwQ5eMwrCB2L5ZT5W+r5/JuMcwZJWUlY+EOvPxzoAQkFGxmFJIUjmL4lFqcjwYEsYmIO7KRZiJRY5GewGa2JzcJ+PRT3U5rCmlSSKbbHZaiw/kaFKWFMuH1zSLv3kw8tqLKIzGRcOK4aDW3PMUKTF0Bpaj0VDhkph0Y3RNcJUQppM0Ykkad5SbMFIY0dYlZ9JuJgo2Y87vx7KF2Kjy32hsytnJKcDGbJUAaRqCJ+P5VcyEtjFY0WsDjHh9qMNvo/EOVyIjcm4wi43I8oqzAlIY9WQHWM+tiVDcFgyjHX5Gqv6xtN1eIxNY5XssH7wKWyqMqKPAktONNIAsLoiro+VkWo4FuX0k+CrsayFJcNYryVW8Ix+cLAWy2LYMYxVOhyr2tFgV2MD2HMQREGKP3Ax+pZXYjMOW3jC6Nzib869BrOxTGoigi2lIPIibBBeRovqNPYE2xzyc2xzoyHqv2LrJMQ2w7rFDv8WG2G7ur/0F2PfiV64mLuPWL1xBdhJbMFIFeAHg/+KTVAeplw8zT1slP5awr3vjZalo1gWI2c0JfEsfqaNxtewK1NUviH6WJW6X68OXOHPOeJJMW13KnGXT3/sJOhi5RHWFO4DSizw+v8LXfILvDsdGwPuR4S39xXrcyDkeH9dLHOwE11stoeh9rHsiL3w6Ird8NT7P7HusR72I0mxHxXe0Cd95rX4oqg1NQAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  font-size: 24rpx;
  font-weight: 400;
  color: #C87E26;
  margin-bottom: 10rpx;
  box-sizing: border-box;
  padding-left: 60rpx;
}
.main .show .one .onesecond.data-v-19409da7 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-size: 40rpx;
  font-weight: bold;
  color: #C87E26;
  align-items: center;
  margin-bottom: 26rpx;
}
.main .show .one .onesecond .onesecondright.data-v-19409da7 {
  background: #FFFFFF;
  font-size: 28rpx;
  font-weight: 400;
  color: #DF3D36;
  padding: 10rpx 24rpx;
  border-radius: 27rpx;
}
.main .show .one .onethired.data-v-19409da7 {
  font-size: 24rpx;
  font-weight: 400;
  color: #635F58;
  margin-bottom: 16rpx;
}
.main .show .two.data-v-19409da7 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0rpx;
  top: 318rpx;
}
.main .show .two .twofirst.data-v-19409da7 {
  width: 100%;
  height: 79rpx;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAvAAAABPCAMAAAC6ewHBAAAAY1BMVEUAAABGQz1HPz5GQjxGQz1GQz1GQz1FQj1FQzxGQzxFQz1GQzxGQz1EQz1DQzxGQz1GQjxGQjxGQz1HQz1GQTxGRT1HRT1GQz1GQz1GQz1GQz1FQjxGQzxGQz1GQjtGQz5GQz12vs3fAAAAIHRSTlMA7xDSxKmXRCJPiWi1KgZ+dWLdPzMVDPfln4NWXbwdOZPVYzwAAARmSURBVHja7d3beqIwFIbhQEiAsBVQZKOu+7/KSdqpjtMZN7XaJPzvBXj0uZ6VgC0jyQCWg6hiAAtRMULxsBgVMdIUA1gARcTIkCUD8J2kj+ApDxmA18KcTsETTxiAxxJOp+C1IGYA3ooDOgselzXgs4p+Y3SSrxmAh9Y5fQreCFIG4J00oPPgT+SGAXhlI+kPjM41OLuCV+KGLgSvDbiSB2+EA9GV4Gk7MwAvzFu6FrwRFQzAeUVEnzD6J4kbSnDcWhLdHDwFNd4nA4eVdUC3B280EwNw1NQQ3RW8wfEcCpyUcqK7gzci3MqDc+KI6FLwSB48kkZ0CaNrIiw24IyZ02WMruMzbmzAAeWqIXo4eGNb430DsFxYb+k6RrcJFH4DCBZLVED0fcEbomUAVmoF3YjRHXiGzQasE2ac6AnBG4HsGIBFOhkQ3Rs8xjw46TTcnxW80Q/Y5sEC7dATPT94oxlxaQM/Khkb+gJGXxVhtYGfEmYRfQ2jB+QzmoeXC+ecvozRQ3ox4Q97wAttUtHTAxg9KhApXrWBlyhTEZD2Y8GfmsechyfbvNduQ/Ban6/wu294mvUq74nInuCNqMZdJTxBUkekWRe8xscDA/hGh5ETka3Ba4HAcgPftci8re12B29w1eLmBh5StoqT5kTw74N+zwC+ZH8c7c4Eb3CZ4lEs3ClMJacjp4I3ohHbDdyqbMeIzjgXvNZHFaKH67FXUU9/czF4o99VMZ7Gwn9s4nH379hdDf5NpLDTw9/206Wd3engDT7M+HcL8FuRDQ1d5HzwRpDXLfabhdu0dR7QVV4E/4bLFUb9QhWrm7cYf4I3gp1K8XhqUfap2gV0O7+Cf7PNK1S/BPu0yrd0Jw+D/5j1EzYcbxWTmes2sCX4N0EkVwUeUXmlLFYysqN1+4J/0/OhjvGCsQfWcT3wnuxiX/Afi72aO1xdOmrTzern13Wngn/XiHHCjuOSsphG0ZC97A7+XZOr1QHvJFguPKxUbnPq7gR/PNHWMX4lbqEkrq06l3oS/Luei3E+4ExrhfVhHoV1x1K/gj+Oe3T/c0zplXBmqPsQ/G8BFyqLcax9lbKIMyW4k6V7EfyHbSTGVZwg/Ccpk3g1isjOi8YlBn8Kf9DhF7i//yabQoc+eBG6n8GfVp1cVij/kc4rmTu9uiwr+L/KTzscb2+w7lJvO19K8Cdbng9jlh4STP0zm+SQZuOQc5/2FgR/PvUjIassbYvFzv110aZZJUXk9zRH8J9sj/HvPZ/8m/1H5EuZ5Qj+il7Xnw+qyqa4Szx4dSdMunjKajXkunG3Hof+AcG/StDo/oVUdTanbZesLb/kL9dJ16azDlwKXXizuF0FwX+33nwFdmKQqqqzeYrbrkjCkL1cGCZF18aTjrtSchA7kzcmOIJ/lSBozDdhJ8QgpVJ1XWdZNk1TrHVakRj78KhkR2V4tE+MotNiTX+A/hj9YUpJXbXYma6bAIMbwQMgeAAED4DgYVEQPCzKL3GHuSVMJI+nAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}
.main .show .two .twosecond.data-v-19409da7 {
  width: 100%;
  height: 100%;
  overflow-y: hidden;
  background-color: #46433D;
}
.main .show .two .twosecond .tsone.data-v-19409da7 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 400;
  color: #FDC894;
  padding-left: 41rpx;
  padding-top: 41rpx;
}
.main .show .two .twosecond .tsone image.data-v-19409da7 {
  width: 42rpx;
  height: 39rpx;
  margin-left: 13rpx;
}
.main .show .two .twosecond .tstwo.data-v-19409da7 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 0 70rpx;
  margin-top: 36rpx;
}
.main .show .two .twosecond .tstwo .tstwomap.data-v-19409da7 {
  font-size: 24rpx;
  font-weight: 400;
  color: #FDC894;
  text-align: center;
}
.main .show .two .twosecond .tstwo .tstwomap image.data-v-19409da7 {
  width: 84rpx;
  height: 84rpx;
}
.main .show .two .twosecond .tsthree.data-v-19409da7 {
  width: 688rpx;
  height: 278rpx;
  margin: 0 auto;
  margin-top: 70rpx;
  background: linear-gradient(180deg, #FFE7BF 0%, #FDC894 100%);
  border-radius: 20rpx;
  box-sizing: border-box;
  padding: 10rpx 19rpx;
}
.main .show .two .twosecond .tsthree .tstfirst.data-v-19409da7 {
  font-size: 30rpx;
  font-weight: bold;
  color: #4F3832;
  margin-bottom: 6rpx;
}
.main .show .two .twosecond .tsthree .tstfirst ._span.data-v-19409da7 {
  color: #EC651C;
}
.main .show .two .twosecond .tsthree .tstsecond.data-v-19409da7 {
  font-size: 22rpx;
  font-weight: 400;
  color: #4F3832;
  margin-bottom: 14rpx;
}
.main .show .two .twosecond .tsthree .tstmap.data-v-19409da7 {
  width: 650rpx;
  height: 129rpx;
  background: #343136;
  box-shadow: inset 0rpx 0rpx 26rpx 1rpx #F7AA44;
  border-radius: 24rpx;
  border: 3rpx solid #EC651C;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  padding: 0 21rpx 0 33rpx;
  box-sizing: border-box;
  position: relative;
  align-items: center;
}
.main .show .two .twosecond .tsthree .tstmap .tstmapleft.data-v-19409da7 {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.main .show .two .twosecond .tsthree .tstmap .tstmapleft .tstmapone.data-v-19409da7 {
  font-size: 32rpx;
  font-weight: 400;
  color: #FFFFFF;
}
.main .show .two .twosecond .tsthree .tstmap .tstmapleft .tstmaptwo.data-v-19409da7 {
  font-size: 24rpx;
  font-family: Yu Gothic UI-Regular, Yu Gothic UI;
  font-weight: 400;
  color: #FFA02E;
}
.main .show .two .twosecond .tsthree .tstmap .tstmapright.data-v-19409da7 {
  display: flex;
  font-size: 36rpx;
  font-weight: bold;
  color: #FFA02E;
  align-items: center;
}
.main .show .two .twosecond .tsthree .tstmap .tstmapright .tstmaprighttxt.data-v-19409da7 {
  font-size: 22rpx;
  font-weight: 400;
  color: #FFFFFF;
  text-decoration: line-through;
  margin-right: 10rpx;
}
.main .show .two .twosecond .tsthree .tstmap .tstmapzhong.data-v-19409da7 {
  width: 166rpx;
  height: 53rpx;
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJUAAAAwBAMAAADqXSUhAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAbUExURUdwTPlKHfpcJvpgJ/lXIflSIfleJ/lvMPlnKze4Wg4AAAAFdFJOUwDjdrQxh6qXAwAAAT1JREFUSMfNlTtPwzAUhQ/UYjZDgbFIRGUMSDzG8JIYWZw5SyKPSCA5I2Jw/LO5bhrU8FALPkO/2M5VnHw6tqIEiNxeuRTOCwyouUvlZHClq5yb9aoJQeVeetchw+VyWqxlsHnbtq5L8rRRITum2hC6IEMCXeRVligmCu/AYyDRAacsVyhw7b0P1vqQcthIjiMZG+9Tu5AjVl9ttlnfx/dLCxrNT9gN+jee4RsS9pdc/+INBzSXxmXJokJWk5Bce2Vp6kXv20q5YVsg5wq7xkhhPqeM+avMRESpoep6LBvKOG/W1MsnBLleAZkhoeUDxnJJLlzwcsXdp/AU/x4PvFzAMS+XLPNsP4FRrlTus5VcydzRcgk3vFxQU55LgtHWKG88LxemRFdGdO0QXROiSxFd0ERXTnTNttRVbKkLH8N4fRMf+peTAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  font-size: 20rpx;
  font-weight: 400;
  color: #F7FAFB;
  text-align: center;
}
.main .show .two .twosecond .tsthree .tstthired.data-v-19409da7 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 10rpx 18rpx;
}
.main .show .two .twosecond .tsthree .tstthired .tstthiredtxt.data-v-19409da7 {
  font-size: 24rpx;
  font-weight: bold;
  color: #4F3832;
}
.main .show .two .twosecond .tsfour.data-v-19409da7 {
  position: fixed;
  bottom: 0rpx;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  padding: 21rpx 31rpx 21rpx 50rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.main .show .two .twosecond .tsfour .tsfourleft.data-v-19409da7 {
  font-size: 36rpx;
  font-weight: bold;
  color: #EC651C;
}
.main .show .two .twosecond .tsfour .tsfourright.data-v-19409da7 {
  background: linear-gradient(180deg, #FFE7BF 0%, #FDC894 100%);
  border-radius: 36rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: Microsoft JhengHei UI-Regular, Microsoft JhengHei UI;
  font-weight: 400;
  color: #EC651C;
  padding: 29rpx 37rpx;
}

