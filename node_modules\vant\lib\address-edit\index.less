@import '../style/var';

.van-address-edit {
  padding: @address-edit-padding;

  &__fields {
    overflow: hidden;
    border-radius: @padding-xs;

    .van-field__label {
      width: 4.1em;
    }
  }

  &__default {
    margin-top: @padding-sm;
    overflow: hidden;
    border-radius: @padding-xs;
  }

  &__buttons {
    padding: @address-edit-buttons-padding;

    .van-button {
      margin-bottom: @address-edit-button-margin-bottom;
    }
  }

  &-detail {
    padding: 0;

    &__search-item {
      background-color: @gray-2;
    }

    &__keyword {
      color: @red;
    }

    &__finish {
      color: @address-edit-detail-finish-color;
      font-size: @address-edit-detail-finish-font-size;
    }
  }
}
