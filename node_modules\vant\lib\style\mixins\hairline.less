@import '../var';

.hairline-common() {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
}

.hairline(@color: @border-color) {
  .hairline-common();

  top: -50%;
  right: -50%;
  bottom: -50%;
  left: -50%;
  border: 0 solid @color;
  transform: scale(0.5);
}

.hairline-top(@color: @border-color, @left: 0, @right: 0) {
  .hairline-common();

  top: 0;
  right: @right;
  left: @left;
  border-top: 1px solid @color;
  transform: scaleY(0.5);
}

.hairline-bottom(@color: @border-color, @left: 0, @right: 0) {
  .hairline-common();

  right: @right;
  bottom: 0;
  left: @left;
  border-bottom: 1px solid @color;
  transform: scaleY(0.5);
}
