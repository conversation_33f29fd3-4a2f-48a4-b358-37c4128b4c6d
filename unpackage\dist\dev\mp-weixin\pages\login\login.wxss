@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.icon.data-v-b237504c {
  font-size: 38rpx;
}
.content.reg-page.data-v-b237504c {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  height: 100vh;
  padding: 30rpx 86rpx;
  position: relative;
  z-index: 9;
  background-color: #242225;
  text-align: center;
}
.content.reg-page image.data-v-b237504c {
  width: 320rpx;
  height: 320rpx;
}
.reg-title.data-v-b237504c {
  font-size: 50rpx;
  margin: 110rpx 0 90rpx;
  color: #fff;
}
.reg-page-btn.data-v-b237504c {
  margin-top: 100rpx;
}
.go-login.data-v-b237504c {
  color: #7E8388;
  font-size: 26rpx;
  text-align: right;
}
.operate-btn button.data-v-b237504c {
  font-size: 32rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
}
.reset.data-v-b237504c {
  color: #666;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  margin-top: 60rpx;
  white-space: nowrap;
}
.reset checkbox.data-v-b237504c {
  -webkit-transform: scale(0.6);
          transform: scale(0.6);
}
.reset .line.data-v-b237504c {
  margin: 0 2em;
}
.reset .text-my-blue.data-v-b237504c {
  color: #FF752B;
}
/* .content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.logo {
	height: 200rpx;
	width: 200rpx;
	margin-top: 200rpx;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 50rpx;
}

.text-area {
	display: flex;
	justify-content: center;
}

.title {
	font-size: 36rpx;
	color: #8f8f94;
} */
/* 599状态码信息弹窗样式 */
.popup-overlay.data-v-b237504c {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  -webkit-animation: fadeIn-data-v-b237504c 0.3s ease-out;
          animation: fadeIn-data-v-b237504c 0.3s ease-out;
}
@-webkit-keyframes fadeIn-data-v-b237504c {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes fadeIn-data-v-b237504c {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@-webkit-keyframes slideUp-data-v-b237504c {
from {
    opacity: 0;
    -webkit-transform: translateY(60rpx) scale(0.95);
            transform: translateY(60rpx) scale(0.95);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
@keyframes slideUp-data-v-b237504c {
from {
    opacity: 0;
    -webkit-transform: translateY(60rpx) scale(0.95);
            transform: translateY(60rpx) scale(0.95);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
.follow-guide-popup.data-v-b237504c {
  width: 640rpx;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx 40rpx;
  box-sizing: border-box;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  -webkit-animation: slideUp-data-v-b237504c 0.4s ease-out;
          animation: slideUp-data-v-b237504c 0.4s ease-out;
}
.follow-guide-popup .popup-header.data-v-b237504c {
  margin-bottom: 32rpx;
}
.follow-guide-popup .popup-header .popup-title.data-v-b237504c {
  display: block;
  font-size: 38rpx;
  font-weight: bold;
  color: #1a1a1a;
}
.follow-guide-popup .popup-content.data-v-b237504c {
  margin-bottom: 36rpx;
}
.follow-guide-popup .popup-content .main-tip.data-v-b237504c {
  display: block;
  font-size: 32rpx;
  color: #333;
  line-height: 48rpx;
  font-weight: 500;
}
.follow-guide-popup .qr-container .qr-section.data-v-b237504c {
  margin-bottom: 32rpx;
}
.follow-guide-popup .qr-container .qr-section .qr-code.data-v-b237504c {
  width: 320rpx;
  height: 320rpx;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}
.follow-guide-popup .qr-container .qr-section .qr-tip.data-v-b237504c {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 42rpx;
  padding: 0 20rpx;
}
.follow-guide-popup .qr-container .important-notice.data-v-b237504c {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
  border: 1rpx solid #ffd591;
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  text-align: left;
  position: relative;
  overflow: hidden;
}
.follow-guide-popup .qr-container .important-notice.data-v-b237504c::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  -webkit-animation: shimmer-data-v-b237504c 3s infinite;
          animation: shimmer-data-v-b237504c 3s infinite;
}
@-webkit-keyframes shimmer-data-v-b237504c {
0% {
    left: -100%;
}
50% {
    left: 100%;
}
100% {
    left: 100%;
}
}
@keyframes shimmer-data-v-b237504c {
0% {
    left: -100%;
}
50% {
    left: 100%;
}
100% {
    left: 100%;
}
}
.follow-guide-popup .qr-container .important-notice .notice-header.data-v-b237504c {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.follow-guide-popup .qr-container .important-notice .notice-header .notice-icon.data-v-b237504c {
  font-size: 32rpx;
  margin-right: 8rpx;
}
.follow-guide-popup .qr-container .important-notice .notice-header .notice-title.data-v-b237504c {
  font-size: 30rpx;
  font-weight: bold;
  color: #d46b08;
}
.follow-guide-popup .qr-container .important-notice .notice-list .notice-item.data-v-b237504c {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}
.follow-guide-popup .qr-container .important-notice .notice-list .notice-item.data-v-b237504c:last-child {
  margin-bottom: 0;
}
.follow-guide-popup .qr-container .important-notice .notice-list .notice-item .bullet.data-v-b237504c {
  font-size: 28rpx;
  color: #d46b08;
  margin-right: 12rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}
.follow-guide-popup .qr-container .important-notice .notice-list .notice-item .notice-text.data-v-b237504c {
  font-size: 26rpx;
  color: #8c4a00;
  line-height: 38rpx;
  flex: 1;
}

