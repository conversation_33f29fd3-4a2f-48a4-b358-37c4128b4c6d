@import '../style/var';

.van-radio {
  display: flex;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  user-select: none;

  &--disabled {
    cursor: not-allowed;
  }

  &--label-disabled {
    cursor: default;
  }

  &--horizontal {
    margin-right: @padding-sm;
  }

  &__icon {
    flex: none;
    height: 1em;
    font-size: @radio-size;
    line-height: 1em;
    cursor: pointer;

    .van-icon {
      display: block;
      box-sizing: border-box;
      width: 1.25em;
      height: 1.25em;
      color: transparent;
      font-size: 0.8em;
      line-height: 1.25;
      text-align: center;
      border: 1px solid @radio-border-color;
      transition-duration: @radio-transition-duration;
      transition-property: color, border-color, background-color;
    }

    &--round {
      .van-icon {
        border-radius: 100%;
      }
    }

    &--checked {
      .van-icon {
        color: @white;
        background-color: @radio-checked-icon-color;
        border-color: @radio-checked-icon-color;
      }
    }

    &--disabled {
      cursor: not-allowed;

      .van-icon {
        background-color: @radio-disabled-background-color;
        border-color: @radio-disabled-icon-color;
      }
    }

    &--disabled&--checked {
      .van-icon {
        color: @radio-disabled-icon-color;
      }
    }
  }

  &__label {
    margin-left: @radio-label-margin;
    color: @radio-label-color;
    line-height: @radio-size;

    &--left {
      margin: 0 @radio-label-margin 0 0;
    }

    &--disabled {
      color: @radio-disabled-label-color;
    }
  }
}
