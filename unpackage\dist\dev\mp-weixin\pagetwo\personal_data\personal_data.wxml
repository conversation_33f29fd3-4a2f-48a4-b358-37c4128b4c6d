<view class="main data-v-7de3d0b3"><view class="show data-v-7de3d0b3"><view class="sone data-v-7de3d0b3"><block wx:if="{{ktype!=0}}"><input placeholder="请输入真实姓名(退款用)，非必填" data-event-opts="{{[['input',[['__set_model',['','name','$event',[]]]]]]}}" value="{{name}}" bindinput="__e" class="data-v-7de3d0b3"/></block><input placeholder="输入手机号码" type="number" data-event-opts="{{[['input',[['__set_model',['','phone','$event',[]]],['changeInput']]]]}}" value="{{phone}}" bindinput="__e" class="data-v-7de3d0b3"/></view><block wx:if="{{yzshow}}"><view class="yztap data-v-7de3d0b3"><input placeholder="输入验证码" type="number" focus="{{setfocus}}" maxlength="4" data-event-opts="{{[['blur',[['exitfocus',['$event']]]],['input',[['__set_model',['','yzma','$event',[]]]]]]}}" value="{{yzma}}" bindblur="__e" bindinput="__e" class="data-v-7de3d0b3"/><block wx:if="{{Verification}}"><view data-event-opts="{{[['tap',[['handleClick',['$event']]]]]}}" class="yanbtn data-v-7de3d0b3" bindtap="__e">获取验证码</view></block><block wx:if="{{!Verification}}"><view class="yanbtn1 data-v-7de3d0b3"><label class="_span data-v-7de3d0b3">{{timer}}</label>s</view></block></view></block><block wx:if="{{hkshow}}"><no_captcha bind:child="__e" vue-id="3e96c6b3-1" data-event-opts="{{[['^child',[['parentEvent']]]]}}" class="data-v-7de3d0b3" bind:__l="__l"></no_captcha></block><view data-event-opts="{{[['tap',[['xgbtn',['$event']]]]]}}" class="stwo data-v-7de3d0b3" bindtap="__e">确认提交</view><view data-event-opts="{{[['tap',[['jbbtn',['$event']]]]]}}" class="stwo data-v-7de3d0b3" bindtap="__e">解绑手机号</view><view class="sthree data-v-7de3d0b3"><view class="sthreehand data-v-7de3d0b3">绑定手机号说明:</view><view class="data-v-7de3d0b3">1.为了更好的为您服务，请先绑定手机号;<view class="_br data-v-7de3d0b3"></view>2.我公司承诺不会泄露用户隐私，也不会对用户进行电话骚扰;<view class="_br data-v-7de3d0b3"></view>3.如有疑问：请联系当前小区运营商;</view></view><van-popup vue-id="3e96c6b3-2" show="{{phoneshow}}" close-on-click-overlay="{{false}}" data-com-type="wx" class="data-v-7de3d0b3" bind:__l="__l" vue-slots="{{['default']}}"><view class="tishi data-v-7de3d0b3"><view class="tstou data-v-7de3d0b3">提示</view><view class="tsnei data-v-7de3d0b3">您尚未绑定手机号，无法进行其他操作，请先绑定手机号</view><view data-event-opts="{{[['tap',[['kbtngo',['$event']]]]]}}" class="kbtn data-v-7de3d0b3" bindtap="__e">确认</view></view></van-popup></view><van-toast vue-id="3e96c6b3-3" id="van-toast" data-com-type="wx" class="data-v-7de3d0b3" bind:__l="__l"></van-toast><van-dialog vue-id="3e96c6b3-4" id="van-dialog" data-com-type="wx" class="data-v-7de3d0b3" bind:__l="__l"></van-dialog></view>