<view class="main data-v-d4040546"><view class="show data-v-d4040546"><van-tabs vue-id="29c22f49-1" active="{{active}}" data-event-opts="{{[['^change',[['gbtn']]]]}}" data-com-type="wx" bind:change="__e" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default']}}"><van-tab vue-id="{{('29c22f49-2')+','+('29c22f49-1')}}" title="充电记录" name="a" data-com-type="wx" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default']}}"><view class="tapthree data-v-d4040546"><block wx:if="{{value}}"><view class="cdjl-0 data-v-d4040546"><uni-datetime-picker vue-id="{{('29c22f49-3')+','+('29c22f49-2')}}" type="daterange" value="{{value}}" data-event-opts="{{[['^change',[['timechange']]],['^input',[['__set_model',['','value','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-d4040546" bind:__l="__l"></uni-datetime-picker></view></block><van-collapse vue-id="{{('29c22f49-4')+','+('29c22f49-2')}}" value="{{activeNames}}" data-event-opts="{{[['^change',[['onchange']]]]}}" data-com-type="wx" bind:change="__e" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><van-collapse-item vue-id="{{('29c22f49-5-'+index)+','+('29c22f49-4')}}" name="{{index}}" data-com-type="wx" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default','title']}}"><view class="data-v-d4040546" slot="title"><view class="tphand data-v-d4040546">消费金额:<label style="color:#FFA02E;margin-left:20rpx;" class="_span data-v-d4040546">{{"¥"+(item.$orig.realMoney+item.$orig.amount)}}</label></view><view class="tpfoot data-v-d4040546">{{item.$orig.startTime}}</view></view><view class="showtap data-v-d4040546"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">订单编号:</view><view class="stoneright data-v-d4040546">{{''+item.$orig.orderNum+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">消费位置:</view><view class="stoneright data-v-d4040546" style="width:75%;">{{''+item.$orig.projectName+"（"+item.$orig.areaName+'）'}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">消费类型:</view><block wx:if="{{item.$orig.type==0}}"><view class="stoneright data-v-d4040546">临时充电</view></block><block wx:if="{{item.$orig.type==1}}"><view class="stoneright data-v-d4040546">本区</view></block><block wx:if="{{item.$orig.type==2}}"><view class="stoneright data-v-d4040546">跨区</view></block></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">充电类型:</view><view class="stoneright data-v-d4040546"><block wx:if="{{item.$orig.chargeTypeStr=='V1'}}"><label class="_span data-v-d4040546">VIP</label></block><block wx:else><block wx:if="{{item.$orig.chargeTypeStr=='V2'}}"><label class="_span data-v-d4040546">公众号VIP</label></block><block wx:else><block wx:if="{{item.$orig.chargeTypeStr=='V3'}}"><label class="_span data-v-d4040546">全平台VIP</label></block><block wx:else><block wx:if="{{item.$orig.chargeTypeStr=='B5'}}"><label class="_span data-v-d4040546">计量2</label></block><block wx:else><label class="_span data-v-d4040546">{{item.$orig.chargeTypeStr}}</label></block></block></block></block></view></view><block wx:if="{{item.$orig.chargeType=='B2'||item.$orig.chargeType=='B5'}}"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546"><block wx:if="{{item.$orig.chargeType=='B4'}}"><label class="_span data-v-d4040546">电量费率(时):</label></block><block wx:if="{{item.$orig.chargeType=='B1'}}"><label class="_span data-v-d4040546">电量费率(元/时):</label></block><block wx:if="{{item.$orig.chargeType=='B2'||item.$orig.chargeType=='B5'}}"><label class="_span data-v-d4040546">电量费率(元/度):</label></block><block wx:if="{{item.$orig.chargeType=='B3'}}"><label class="_span data-v-d4040546">电量费率(元/次):</label></block></view><block wx:if="{{item.$orig.rate}}"><view class="stoneright data-v-d4040546">{{''+item.$orig.rate+''}}</view></block><block wx:if="{{!item.$orig.rate}}"><view class="stoneright data-v-d4040546">0</view></block></view></block><block wx:if="{{item.$orig.chargeType=='B2'||item.$orig.chargeType=='B5'}}"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546"><block wx:if="{{item.$orig.chargeType=='B2'}}"><label class="_span data-v-d4040546">服务费费率(元/小时)</label></block><block wx:if="{{item.$orig.chargeType=='B5'}}"><label class="_span data-v-d4040546">服务费费率(元/度)</label></block></view><block wx:if="{{item.$orig.spareVar8}}"><view class="stoneright data-v-d4040546">{{''+item.$orig.spareVar8+''}}</view></block><block wx:if="{{!item.$orig.spareVar8}}"><view class="stoneright data-v-d4040546">0</view></block></view></block><block wx:if="{{item.$orig.chargeType=='B2'||item.$orig.chargeType=='B5'}}"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">充电电费(元)</view><block wx:if="{{item.$orig.spareVar7}}"><view class="stoneright data-v-d4040546">{{''+item.$orig.spareVar7+''}}</view></block><block wx:if="{{!item.$orig.spareVar7}}"><view class="stoneright data-v-d4040546">0</view></block></view></block><block wx:if="{{item.$orig.chargeType=='B2'||item.$orig.chargeType=='B5'}}"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">充电电量(度):</view><view class="stoneright data-v-d4040546">{{''+item.$orig.chargeKwh+''}}</view></view></block><block wx:if="{{item.$orig.chargeType=='B2'||item.$orig.chargeType=='B5'}}"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">服务费(元)</view><block wx:if="{{item.$orig.spareVar9}}"><view class="stoneright data-v-d4040546">{{''+item.$orig.spareVar9+''}}</view></block><block wx:if="{{!item.$orig.spareVar9}}"><view class="stoneright data-v-d4040546">0</view></block></view></block><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">充电器功率(瓦):</view><block wx:if="{{item.$orig.settlePower}}"><view class="stoneright data-v-d4040546">{{''+item.$orig.settlePower+''}}</view></block><block wx:if="{{!item.$orig.settlePower}}"><view class="stoneright data-v-d4040546">0</view></block></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">启动方式:</view><block wx:if="{{item.$orig.qrOrCard=='1'}}"><view class="stoneright data-v-d4040546">电卡</view></block><block wx:if="{{item.$orig.qrOrCard=='2'}}"><view class="stoneright data-v-d4040546">扫码</view></block><block wx:if="{{item.$orig.qrOrCard=='3'}}"><view class="stoneright data-v-d4040546">验证码</view></block><block wx:if="{{item.$orig.qrOrCard=='4'}}"><view class="stoneright data-v-d4040546">蓝牙</view></block></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">结束原因:</view><block wx:if="{{item.$orig.stopReason=='00'}}"><view class="stoneright data-v-d4040546">系统结算</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='01'}}"><view class="stoneright data-v-d4040546">充满自停</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='02'}}"><view class="stoneright data-v-d4040546">达到最大充电时长</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='03'}}"><view class="stoneright data-v-d4040546">达到预设时间</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='04'}}"><view class="stoneright data-v-d4040546">达到预设电量</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='05'}}"><view class="stoneright data-v-d4040546"><block wx:if="{{item.g0=='04'}}"><label class="_span data-v-d4040546">用户拔出</label></block><block wx:else><label class="_span data-v-d4040546">充电结束</label></block></view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='06'}}"><view class="stoneright data-v-d4040546">负载过大</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='07'}}"><view class="stoneright data-v-d4040546">手动停止</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='08'}}"><view class="stoneright data-v-d4040546">功率起伏过大</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='09'}}"><view class="stoneright data-v-d4040546">电池未连接好</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='10'}}"><view class="stoneright data-v-d4040546">水浸断电</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='11'}}"><view class="stoneright data-v-d4040546">灭火结算（本端口）</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='12'}}"><view class="stoneright data-v-d4040546">灭火结算（非本端口）</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='13'}}"><view class="stoneright data-v-d4040546">用户密码开柜断电</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='14'}}"><view class="stoneright data-v-d4040546">未关好柜门</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='15'}}"><view class="stoneright data-v-d4040546">外部操作打开柜门</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='17'}}"><view class="stoneright data-v-d4040546">服务器强制停止</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='0A'}}"><view class="stoneright data-v-d4040546">环境温度过高</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='0B'}}"><view class="stoneright data-v-d4040546">端口温度过高</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='0C'}}"><view class="stoneright data-v-d4040546">过流或短路</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='0D'}}"><view class="stoneright data-v-d4040546">用户拔出-1</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='0E'}}"><view class="stoneright data-v-d4040546">接触不良或保险丝烧断</view></block><block wx:else><block wx:if="{{item.$orig.stopReason=='0F'}}"><view class="stoneright data-v-d4040546">继电器坏或保险丝断</view></block><block wx:else><view class="stoneright data-v-d4040546">{{''+item.$orig.stopReason+''}}</view></block></block></block></block></block></block></block></block></block></block></block></block></block></block></block></block></block></block></block></block></block></block></block></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">充电时长:</view><view class="stoneright data-v-d4040546">{{''+item.$orig.time+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">结算时间:</view><view class="stoneright data-v-d4040546">{{''+item.$orig.endTime+''}}</view></view><block wx:if="{{freeappid=='wxb9f6b0a85cd3d753'&&item.$orig.chargeType=='B2'}}"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">服务费(元):</view><view class="stoneright data-v-d4040546">{{''+item.$orig.serviceFree+''}}</view></view></block><block wx:if="{{freeappid=='wxb9f6b0a85cd3d753'&&item.$orig.chargeType=='B2'}}"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">电费(元):</view><view class="stoneright data-v-d4040546">{{''+item.$orig.powerFree+''}}</view></view></block><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">基本账户消费(元):</view><view class="stoneright data-v-d4040546">{{''+item.$orig.realMoney+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">赠送账户消费(元):</view><view class="stoneright data-v-d4040546">{{''+item.$orig.amount+''}}</view></view><block wx:for="{{item.$orig.kklist}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><block wx:if="{{item.$orig.kuanno==1}}"><navigator class="_a data-v-d4040546"><block wx:if="{{item1.accountType==0}}"><navigator class="_a data-v-d4040546"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">扣款前基本账户余额(元):</view><view class="stoneright data-v-d4040546" style="font-weight:700;">{{''+item1.frontMoney+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">扣款后基本账户余额(元):</view><view class="stoneright data-v-d4040546" style="font-weight:700;">{{''+item1.endMoney+''}}</view></view></navigator></block><block wx:if="{{item1.accountType==1}}"><navigator class="_a data-v-d4040546"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">扣款前赠送账户余额(元):</view><view class="stoneright data-v-d4040546" style="font-weight:700;">{{''+item1.frontMoney+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">扣款后赠送账户余额(元):</view><view class="stoneright data-v-d4040546" style="font-weight:700;">{{''+item1.endMoney+''}}</view></view></navigator></block></navigator></block></block><block wx:if="{{item.$orig.kuanno1==1}}"><navigator class="_a data-v-d4040546"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">免费占用时长(小时):</view><view class="stoneright data-v-d4040546" style="font-weight:700;">{{''+item.$orig.kklist1.occupyFreeStr+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">产生占位时长(小时):</view><view class="stoneright data-v-d4040546" style="font-weight:700;">{{''+item.$orig.kklist1.getMakeTimeStr+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">占位费率:</view><view class="stoneright data-v-d4040546" style="font-weight:700;">{{''+item.$orig.kklist1.occupyRate+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">占位费用:</view><view class="stoneright data-v-d4040546" style="font-weight:700;"><block wx:if="{{item.$orig.kklist1.statuss==0}}"><label class="_span data-v-d4040546">{{item.$orig.kklist1.occupyMoney+"元"}}</label></block><block wx:if="{{item.$orig.kklist1.statuss==1}}"><label class="_span data-v-d4040546">{{item.g1+"小时"}}</label></block></view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">封顶金额:</view><view class="stoneright data-v-d4040546" style="font-weight:700;">{{''+item.$orig.kklist1.occupyMax+''}}<block wx:if="{{item.$orig.kklist1.statuss==0}}"><label class="_span data-v-d4040546">元</label></block><block wx:if="{{item.$orig.kklist1.statuss==1}}"><label class="_span data-v-d4040546">小时</label></block></view></view></navigator></block><block wx:if="{{item.$orig.kuanno3==1}}"><navigator class="_a data-v-d4040546"><view class="stone data-v-d4040546" style="align-items:center;"><view class="stoneleft data-v-d4040546" style="font-weight:700;">充电守护金额(元):</view><view class="stoneright data-v-d4040546" style="font-weight:700;margin-right:15rpx;">{{''+item.$orig.kklist3.moneyReal+''}}</view><block wx:if="{{White==1}}"><view data-event-opts="{{[['tap',[['lxkf',['$0'],[[['bylist','',index,'orderNum']]]]]]]}}" style="color:#FFFFFF;background-color:#4b98ed;width:78px;font-size:15px;text-align:center;border-radius:5px;" bindtap="__e" class="data-v-d4040546">客服</view></block></view></navigator></block><block wx:if="{{item.$orig.kuanno2==1}}"><navigator class="_a data-v-d4040546"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546" style="font-weight:700;">开柜密码:</view><view class="stoneright data-v-d4040546" style="font-weight:700;">{{''+item.$orig.kklist2+''}}</view></view></navigator></block><block wx:if="{{(item.$orig.amount>0||item.$orig.realMoney>0)&&item.$orig.kuanno!=1}}"><view data-event-opts="{{[['tap',[['kkbtn',['$0'],[[['bylist','',index,'orderNum']]]]]]]}}" class="stonebtn data-v-d4040546" bindtap="__e">扣款详情</view></block><block wx:if="{{item.g2}}"><view data-event-opts="{{[['tap',[['zwbtn',['$0'],[[['bylist','',index,'orderNum']]]]]]]}}" class="stonebtn data-v-d4040546" style="width:170rpx;margin-top:10rpx;" bindtap="__e">占位费详情</view></block><block wx:if="{{item.$orig.kuanno3!=1&&item.$orig.spareVar5==1}}"><view data-event-opts="{{[['tap',[['shbtn',['$0'],[[['bylist','',index,'orderNum']]]]]]]}}" class="stonebtn data-v-d4040546" style="width:170rpx;margin-top:10rpx;" bindtap="__e">充电守护</view></block><block wx:if="{{item.g3}}"><view data-event-opts="{{[['tap',[['pwdbtn',['$0'],[[['bylist','',index,'id']]]]]]]}}" class="stonebtn data-v-d4040546" style="width:170rpx;margin-top:10rpx;" bindtap="__e">开柜密码</view></block></view></van-collapse-item></block></van-collapse><uni-load-more vue-id="{{('29c22f49-6')+','+('29c22f49-2')}}" status="{{status}}" icon-size="{{16}}" content-text="{{contentText}}" class="data-v-d4040546" bind:__l="__l"></uni-load-more></view></van-tab><van-tab vue-id="{{('29c22f49-7')+','+('29c22f49-1')}}" title="包月记录" name="b" data-com-type="wx" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default']}}"><view class="tapthree data-v-d4040546"><block wx:if="{{value}}"><view class="cdjl-0 data-v-d4040546"><uni-datetime-picker vue-id="{{('29c22f49-8')+','+('29c22f49-7')}}" type="daterange" value="{{value}}" data-event-opts="{{[['^maskClick',[['maskClick']]],['^input',[['__set_model',['','value','$event',[]]]]]]}}" bind:maskClick="__e" bind:input="__e" class="data-v-d4040546" bind:__l="__l"></uni-datetime-picker></view></block><van-collapse vue-id="{{('29c22f49-9')+','+('29c22f49-7')}}" value="{{activeNames1}}" data-event-opts="{{[['^change',[['onchange1']]]]}}" data-com-type="wx" bind:change="__e" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{yuelist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><van-collapse-item vue-id="{{('29c22f49-10-'+index)+','+('29c22f49-9')}}" name="{{index}}" data-com-type="wx" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default','title']}}"><view class="data-v-d4040546" slot="title"><view class="tphand data-v-d4040546">消费金额:<label style="color:#FFA02E;margin-left:20rpx;" class="_span data-v-d4040546">{{"¥"+item.useMoney}}</label></view><view class="tpfoot data-v-d4040546">{{item.createTime}}</view></view><view class="showtap data-v-d4040546"><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">小区:</view><view class="stoneright data-v-d4040546">{{''+item.projectName+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">使用金额:</view><view class="stoneright data-v-d4040546">{{'¥'+item.useMoney+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">开始月份:</view><view class="stoneright data-v-d4040546">{{''+item.startMonth+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">结束月份:</view><view class="stoneright data-v-d4040546">{{''+item.endMonth+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">失效月份:</view><view class="stoneright data-v-d4040546">{{''+item.invalidMonth+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">购买月数:</view><view class="stoneright data-v-d4040546">{{''+item.monthNums+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">套餐次数/时长:</view><view class="stoneright data-v-d4040546">{{''+item.totals+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">购买总次数/时长:</view><view class="stoneright data-v-d4040546">{{''+item.nums+''}}</view></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">类型:</view><block wx:if="{{item.types=='A1'}}"><view class="stoneright data-v-d4040546">包时</view></block><block wx:if="{{item.types=='A3'}}"><view class="stoneright data-v-d4040546">包次</view></block><block wx:if="{{item.types=='A4'}}"><view class="stoneright data-v-d4040546">30天套餐(包总时)</view></block><block wx:if="{{item.types=='A5'}}"><view class="stoneright data-v-d4040546">30天套餐(按天包时)</view></block><block wx:if="{{item.types=='A6'}}"><view class="stoneright data-v-d4040546">30天套餐(包总次)</view></block><block wx:if="{{item.types=='A7'}}"><view class="stoneright data-v-d4040546">30天套餐(按天包次)</view></block></view><view class="stone data-v-d4040546"><view class="stoneleft data-v-d4040546">购买时间:</view><view class="stoneright data-v-d4040546">{{''+item.createTime+''}}</view></view></view></van-collapse-item></block></van-collapse><uni-load-more vue-id="{{('29c22f49-11')+','+('29c22f49-7')}}" status="{{status1}}" icon-size="{{16}}" content-text="{{contentText1}}" class="data-v-d4040546" bind:__l="__l"></uni-load-more></view></van-tab></van-tabs></view><van-popup vue-id="29c22f49-12" show="{{ptype}}" data-com-type="wx" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default']}}"><view class="pwdtan data-v-d4040546"><view class="pwdtanone data-v-d4040546">开柜密码</view><view class="pwdtanone data-v-d4040546">{{''+kgpwd+''}}</view></view></van-popup><view class="kf data-v-d4040546"><van-popup vue-id="29c22f49-13" show="{{kfshow}}" data-com-type="wx" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default']}}"><view class="kfmap data-v-d4040546"><view data-event-opts="{{[['tap',[['gkf',['$event']]]]]}}" class="one data-v-d4040546" bindtap="__e"><image src="../../static/tabBarimg/kfclose.png" class="data-v-d4040546"></image></view><view class="two data-v-d4040546">专属客服</view><view class="three data-v-d4040546">您可以通过以下方式联系到我哦</view><view class="four data-v-d4040546"><block wx:if="{{xylist.phoneStatuss==1}}"><view data-event-opts="{{[['tap',[['callphone',['$event']]]]]}}" class="fourfirst data-v-d4040546" bindtap="__e"><image src="../../static/tabBarimg/kfphone.png" class="data-v-d4040546"></image>电话客服</view></block><block wx:if="{{xylist.imgStatuss==1&&xylist.phoneStatuss==1}}"><view class="fourthired data-v-d4040546"></view></block><block wx:if="{{xylist.imgStatuss==1}}"><view data-event-opts="{{[['tap',[['zaixian',['$event']]]]]}}" class="foursecond data-v-d4040546" bindtap="__e"><image src="../../static/tabBarimg/kfwx.png" class="data-v-d4040546"></image>在线客服</view></block></view></view></van-popup><van-popup vue-id="29c22f49-14" show="{{wxtcshow}}" data-com-type="wx" class="data-v-d4040546" bind:__l="__l" vue-slots="{{['default']}}"><view class="kfmap data-v-d4040546"><view data-event-opts="{{[['tap',[['gwx',['$event']]]]]}}" class="one data-v-d4040546" bindtap="__e"><image src="../../static/tabBarimg/kfclose.png" class="data-v-d4040546"></image></view><view class="five data-v-d4040546"><image src="{{xylist.wechatImg}}" class="data-v-d4040546"></image></view><view class="six data-v-d4040546"><view class="data-v-d4040546">关注公众号</view><view class="data-v-d4040546">即可享受专属客服在线咨询服务</view></view></view></van-popup></view><van-toast vue-id="29c22f49-15" id="van-toast" data-com-type="wx" class="data-v-d4040546" bind:__l="__l"></van-toast><van-dialog vue-id="29c22f49-16" id="van-dialog" data-com-type="wx" class="data-v-d4040546" bind:__l="__l"></van-dialog></view>