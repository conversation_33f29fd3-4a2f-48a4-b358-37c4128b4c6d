
page{
	height: 100%;
	background-color:#242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-7de3d0b3 {
  width: 100%;
  overflow-x: hidden;
}
.main .hand.data-v-7de3d0b3 {
  text-size-adjust: 100% !important;
  -webkit-text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
  width: 100%;
  background-color: #242225;
  font-size: 30rpx;
  padding: 20rpx 0;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
  color: #ffffff;
  position: relative;
}
.main .hand image.data-v-7de3d0b3 {
  width: 22rpx;
  height: 39rpx;
  position: absolute;
  left: 31rpx;
  top: 22rpx;
}
.main .show.data-v-7de3d0b3 {
  width: 100%;
  margin-top: 79rpx;
  margin-bottom: 41rpx;
}
.main .show .sone.data-v-7de3d0b3 {
  width: 100%;
}
.main .show .sone input.data-v-7de3d0b3 {
  width: 650rpx;
  height: 100rpx;
  box-sizing: border-box;
  margin: 0 auto;
  background: #343136;
  border-radius: 24px;
  padding: 0 40rpx;
  box-sizing: border-box;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  margin-bottom: 40rpx;
}
.main .show .yztap.data-v-7de3d0b3 {
  display: flex;
  justify-content: space-between;
  width: 650rpx;
  height: 140rpx;
  box-sizing: border-box;
  margin: 0 auto;
  background: #343136;
  border-radius: 24px;
  padding: 28rpx 40rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  align-items: center;
}
.main .show .yztap input.data-v-7de3d0b3 {
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
}
.main .show .yztap .yanbtn.data-v-7de3d0b3 {
  font-size: 28rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  line-height: 68rpx;
  height: 68rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  border-radius: 24rpx;
  padding: 0 20rpx;
}
.main .show .yztap .yanbtn1.data-v-7de3d0b3 {
  font-size: 28rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  line-height: 68rpx;
  width: 180rpx;
  height: 68rpx;
  background: #434045;
  border-radius: 24rpx;
}
.main .show .stwo.data-v-7de3d0b3 {
  width: 650rpx;
  height: 100rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  border-radius: 36rpx;
  font-size: 32rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 100rpx;
  margin: 0 auto;
  margin-top: 84rpx;
  color: #ffffff;
}
.main .show.data-v-7de3d0b3 .van-popup {
  padding: 30rpx;
  border-radius: 16rpx;
}
.main .show .tishi .tstou.data-v-7de3d0b3 {
  color: #333333;
  font-size: 32rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
}
.main .show .tishi .tsnei.data-v-7de3d0b3 {
  font-size: 28rpx;
  padding: 30rpx;
  color: #555555;
}
.main .show .tishi .kbtn.data-v-7de3d0b3 {
  width: 570rpx;
  height: 75rpx;
  background: #4b98ed;
  font-size: 30rpx;
  border-radius: 16rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
  line-height: 75rpx;
  margin: 0 auto;
  margin-top: 40rpx;
  color: #ffffff;
}
.main .show .sthree.data-v-7de3d0b3 {
  width: 100%;
  box-sizing: border-box;
  padding: 50rpx;
  font-size: 24rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #D0D0D0;
  line-height: 40rpx;
}
.main .show .sthree .sthreehand.data-v-7de3d0b3 {
  font-size: 32rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #fff;
  margin-bottom: 20rpx;
}

