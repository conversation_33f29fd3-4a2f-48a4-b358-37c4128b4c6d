
page{
	height: 100%;
	background-color:#242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-d4040546 {
  width: 100%;
  background-color: #242225;
}
.main.data-v-d4040546 .van-list__finished-text {
  margin-top: 100rpx;
  padding-bottom: 200rpx;
}
.main.data-v-d4040546 .van-hairline--top-bottom::after {
  border-width: 0;
}
.main .van-popup--center.data-v-d4040546 {
  border-radius: 20rpx;
}
.main .pwdtan.data-v-d4040546 {
  width: 550rpx;
  padding: 10rpx 0;
}
.main .pwdtan .pwdtanone.data-v-d4040546 {
  font-size: 64rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #111111;
  text-align: center;
  padding: 10rpx 0;
}
.main .show.data-v-d4040546 .van-tab {
  font-size: 32rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #999999;
}
.main .show.data-v-d4040546 .van-tab--active {
  font-size: 32rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #FFA02E;
}
.main .show.data-v-d4040546 .van-tabs__line {
  width: 91rpx !important;
  height: 4rpx;
  border-radius: 2px;
  background-color: #FFA02E;
}
.main .show.data-v-d4040546 .van-tabs__nav {
  background-color: #242225;
}
.main .show .tapthree.data-v-d4040546 {
  width: 686rpx;
  margin: 16rpx auto;
}
.main .show .tapthree .cdjl-0.data-v-d4040546 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  background: #343136;
  height: 100rpx;
  align-items: center;
  justify-content: space-around;
  border-radius: 18rpx;
}
.main .show .tapthree .cdjl-0.data-v-d4040546 .uni-date-x--border {
  width: 100%;
  box-sizing: border-box;
  border: 0rpx !important;
}
.main .show .tapthree .cdjl-0.data-v-d4040546 .uni-date-x {
  width: 100%;
  display: flex;
  box-sizing: border-box;
  background: #343136;
  align-items: center;
  padding: 0rpx !important;
  justify-content: space-between !important;
  border-radius: 18rpx;
}
.main .show .tapthree .cdjl-0.data-v-d4040546 .uni-date__x-input {
  height: 60rpx;
  width: 260rpx;
  border-radius: 10rpx;
  border: 2rpx solid #ccc;
  font-size: 30rpx;
  line-height: 60rpx;
  color: #bbbbbb;
  text-align: center;
}
.main .show .tapthree .tphand.data-v-d4040546 {
  font-size: 28rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #fff;
}
.main .show .tapthree .tpfoot.data-v-d4040546 {
  font-size: 24rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  color: #fff;
  margin-top: 10rpx;
}
.main .show .tapthree.data-v-d4040546 .van-collapse-item__content {
  padding: 0 30rpx;
  background-color: #343136;
  border-radius: 0rpx 0rpx 24rpx 24rpx;
}
.main .show .tapthree.data-v-d4040546 .van-cell:after {
  border-bottom: 0rpx;
  border-bottom-color: transparent;
}
.main .show .tapthree.data-v-d4040546 .van-collapse-item {
  border-radius: 24rpx !important;
  background-color: #343136 !important;
  margin-top: 30rpx;
}
.main .show .tapthree.data-v-d4040546 .van-cell {
  background-color: transparent;
  align-items: center;
  padding: 30rpx 40rpx !important;
}
.main .show .tapthree.data-v-d4040546 .van-hairline--top-bottom::after {
  border-width: 0;
}
.main .show .tapthree.data-v-d4040546 .van-hairline--top:after {
  border-top-width: 0rpx;
}
.main .show .tapthree .showtap.data-v-d4040546 {
  width: 100%;
  box-sizing: border-box;
  padding: 40rpx 0;
}
.main .show .tapthree .showtap .stone.data-v-d4040546 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  margin-bottom: 15rpx;
}
.main .show .tapthree .showtap .stone .stoneleft.data-v-d4040546 {
  font-size: 24rpx;
  font-family: PingFang SC Regular, PingFang SC Regular-Regular;
  font-weight: 400;
  color: #D0D0D0;
}
.main .show .tapthree .showtap .stone .stoneright.data-v-d4040546 {
  color: #969799;
  font-size: 24rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  margin-left: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .show .tapthree .showtap .stonebtn.data-v-d4040546 {
  color: #FFFFFF;
  background-color: #4b98ed;
  width: 150rpx;
  font-size: 30rpx;
  text-align: center;
  border-radius: 10rpx;
}
.main .kf.data-v-d4040546 .van-popup {
  border-radius: 36rpx;
  overflow-x: hidden;
}
.main .kf .kfmap.data-v-d4040546 {
  background-color: #ffffff;
  width: 544rpx;
  position: relative;
  padding-bottom: 50rpx;
  padding-top: 58rpx;
  box-sizing: border-box;
}
.main .kf .kfmap .one.data-v-d4040546 {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}
.main .kf .kfmap .one image.data-v-d4040546 {
  width: 29rpx;
  height: 29rpx;
}
.main .kf .kfmap .two.data-v-d4040546 {
  text-align: center;
  font-size: 34rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #111111;
  line-height: 48rpx;
}
.main .kf .kfmap .three.data-v-d4040546 {
  margin-top: 44rpx;
  text-align: center;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #3A4044;
  line-height: 42rpx;
}
.main .kf .kfmap .four.data-v-d4040546 {
  display: flex;
  padding: 0 54rpx;
  justify-content: center;
  align-items: center;
  margin-top: 70rpx;
}
.main .kf .kfmap .four .fourfirst.data-v-d4040546 {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 700;
  color: #3A4044;
  line-height: 48rpx;
}
.main .kf .kfmap .four .fourfirst image.data-v-d4040546 {
  width: 30rpx;
  height: 30rpx;
  margin-right: 5rpx;
}
.main .kf .kfmap .four .foursecond.data-v-d4040546 {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #EC651C;
  line-height: 48rpx;
}
.main .kf .kfmap .four .foursecond image.data-v-d4040546 {
  width: 35rpx;
  height: 29rpx;
  margin-right: 5rpx;
}
.main .kf .kfmap .four .fourthired.data-v-d4040546 {
  width: 1rpx;
  height: 55rpx;
  background: #EEEEEE;
  margin: 0 48rpx;
}
.main .kf .kfmap .five.data-v-d4040546 {
  text-align: center;
}
.main .kf .kfmap .five image.data-v-d4040546 {
  width: 424rpx;
  height: 424rpx;
  background: #D4D4D4;
  border-radius: 10rpx;
}
.main .kf .kfmap .six.data-v-d4040546 {
  text-align: center;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #EC651C;
  line-height: 49rpx;
  margin-top: 50rpx;
}

