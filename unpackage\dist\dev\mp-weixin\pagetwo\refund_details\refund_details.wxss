
page{
	height: 100%;
	background-color:#242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-cfe34782 {
  width: 100%;
  background-color: #242225;
}
.main.data-v-cfe34782 .van-list__finished-text {
  margin-top: 100rpx;
  padding-bottom: 200rpx;
}
.main.data-v-cfe34782 .van-hairline--top-bottom::after {
  border-width: 0;
}
.main.data-v-cfe34782 .van-hairline--top:after {
  border-top-width: 0rpx;
}
.main .show.data-v-cfe34782 .van-tab {
  font-size: 20rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #999999;
  padding: 0rpx;
}
.main .show.data-v-cfe34782 .van-tab--active {
  font-size: 20rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #FFA02E;
}
.main .show.data-v-cfe34782 .van-tabs__line {
  width: 91rpx !important;
  height: 4rpx;
  border-radius: 2px;
  background-color: #FFA02E;
}
.main .show.data-v-cfe34782 .van-tabs__nav {
  background-color: #242225;
}
.main .show .tapthree.data-v-cfe34782 {
  width: 686rpx;
  margin: 16rpx auto;
}
.main .show .tapthree .cdjl-0.data-v-cfe34782 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  background: #343136;
  height: 100rpx;
  align-items: center;
  justify-content: space-around;
  border-radius: 18rpx;
}
.main .show .tapthree .cdjl-0.data-v-cfe34782 .uni-date-x--border {
  width: 100%;
  box-sizing: border-box;
  border: 0rpx !important;
}
.main .show .tapthree .cdjl-0.data-v-cfe34782 .uni-date-x {
  width: 100%;
  display: flex;
  box-sizing: border-box;
  background: #343136;
  align-items: center;
  padding: 0rpx !important;
  justify-content: space-between !important;
  border-radius: 18rpx;
}
.main .show .tapthree .cdjl-0.data-v-cfe34782 .uni-date__x-input {
  height: 60rpx;
  width: 260rpx;
  border-radius: 10rpx;
  border: 2rpx solid #ccc;
  font-size: 30rpx;
  line-height: 60rpx;
  color: #bbbbbb;
  text-align: center;
}
.main .show .tapthree .tphand.data-v-cfe34782 {
  font-size: 28rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #fff;
}
.main .show .tapthree .tpfoot.data-v-cfe34782 {
  font-size: 24rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  color: #fff;
  margin-top: 10rpx;
}
.main .show .tapthree.data-v-cfe34782 .van-collapse-item__content {
  padding: 0 30rpx;
  background-color: #343136;
  border-radius: 0rpx 0rpx 24rpx 24rpx;
}
.main .show .tapthree.data-v-cfe34782 .van-cell:after {
  border-bottom: 0rpx;
  border-bottom-color: transparent;
}
.main .show .tapthree.data-v-cfe34782 .van-collapse-item {
  border-radius: 24rpx !important;
  background-color: #343136 !important;
  margin-top: 30rpx;
}
.main .show .tapthree.data-v-cfe34782 .van-cell {
  background-color: transparent;
  align-items: center;
  padding: 30rpx 40rpx !important;
}
.main .show .tapthree .showtap.data-v-cfe34782 {
  width: 100%;
  box-sizing: border-box;
  padding: 40rpx 0;
}
.main .show .tapthree .showtap .stone.data-v-cfe34782 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  margin-bottom: 15rpx;
}
.main .show .tapthree .showtap .stone .stoneleft.data-v-cfe34782 {
  font-size: 24rpx;
  font-family: PingFang SC Regular, PingFang SC Regular-Regular;
  font-weight: 400;
  color: #D0D0D0;
  width: 45%;
}
.main .show .tapthree .showtap .stone .stoneright.data-v-cfe34782 {
  font-size: 24rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  color: #969799;
}

