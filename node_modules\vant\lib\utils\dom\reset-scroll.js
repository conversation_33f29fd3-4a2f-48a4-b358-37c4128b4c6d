"use strict";

exports.__esModule = true;
exports.resetScroll = resetScroll;

var _system = require("../validate/system");

var _scroll = require("./scroll");

/**
 * Hack for iOS12 page scroll
 * https://developers.weixin.qq.com/community/develop/doc/00044ae90742f8c82fb78fcae56800
 */
var isIOS = (0, _system.isIOS)();
/* istanbul ignore next */

function resetScroll() {
  if (isIOS) {
    (0, _scroll.setRootScrollTop)((0, _scroll.getRootScrollTop)());
  }
}