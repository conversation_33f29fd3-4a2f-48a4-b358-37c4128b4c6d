(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/login/login"],{

/***/ 75:
/*!**************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/微信图片视频/备份代码/66_Chargingpile_applet_2.0/main.js?{"page":"pages%2Flogin%2Flogin"} ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _login = _interopRequireDefault(__webpack_require__(/*! ./pages/login/login.vue */ 76));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_login.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 76:
/*!*******************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/微信图片视频/备份代码/66_Chargingpile_applet_2.0/pages/login/login.vue ***!
  \*******************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _login_vue_vue_type_template_id_b237504c_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./login.vue?vue&type=template&id=b237504c&scoped=true& */ 77);
/* harmony import */ var _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./login.vue?vue&type=script&lang=js& */ 79);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _login_vue_vue_type_style_index_0_id_b237504c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true& */ 81);
/* harmony import */ var _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 33);

var renderjs





/* normalize component */

var component = Object(_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _login_vue_vue_type_template_id_b237504c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _login_vue_vue_type_template_id_b237504c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "b237504c",
  null,
  false,
  _login_vue_vue_type_template_id_b237504c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/login/login.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 77:
/*!**************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/微信图片视频/备份代码/66_Chargingpile_applet_2.0/pages/login/login.vue?vue&type=template&id=b237504c&scoped=true& ***!
  \**************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&scoped=true& */ 78);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 78:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/微信图片视频/备份代码/66_Chargingpile_applet_2.0/pages/login/login.vue?vue&type=template&id=b237504c&scoped=true& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      $event.stopPropagation()
      _vm.isXieyi = !_vm.isXieyi
    }
  }
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 79:
/*!********************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/微信图片视频/备份代码/66_Chargingpile_applet_2.0/pages/login/login.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js& */ 80);
/* harmony import */ var _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 80:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/微信图片视频/备份代码/66_Chargingpile_applet_2.0/pages/login/login.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toast = _interopRequireDefault(__webpack_require__(/*! ../../wxcomponents/vant/toast/toast */ 53));
var _dialog = _interopRequireDefault(__webpack_require__(/*! ../../wxcomponents/vant/dialog/dialog */ 35));
var _Config = __webpack_require__(/*! ../../common/Config.js */ 30);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      oplist: '',
      $imgUrl: _Config.Config.imgUrl,
      submitToggle: false,
      isXieyi: false,
      isGetMobile: true,
      loginCode: "",
      showFollowGuide: false,
      // 是否显示599状态码信息弹窗
      qrCodeUrl: '',
      // 公众号二维码URL
      debugReason: '',
      // 调试用的reason参数
      option: null,
      // 页面参数
      hasRedirected: false,
      // 防止重复跳转标记
      showPhoneAuthBtn: false // 是否显示手机号授权按钮
    };
  },
  onLoad: function onLoad(options) {
    var _this2 = this;
    // 保存页面参数
    this.option = options;
    console.log('Login页面onLoad，接收到的参数:', options);

    // 检查是否是来自token过期的重定向
    if (options && options.source === 'token_expired') {
      console.log('检测到来自token过期的重定向，来源页面:', options.returnType);
      // 重定向信息已经在其他页面保存到本地存储中，这里只需要记录日志
    }

    // 处理扫码参数
    if (options) {
      // 如果有q参数（扫码链接）
      if (options.q) {
        this.oplist = decodeURIComponent(options.q);
      }
      // 如果直接传入device和port参数（优先处理，不管是否有reason参数）
      else if (options.device && options.port) {
        // 构造与oplist相同格式的字符串，便于后续处理
        this.oplist = "".concat(_Config.Config.baseUrl, "/ap3200/").concat(options.device, "/").concat(options.port);
      }
      // 检查App全局参数
      else {
        var app = getApp();
        if (app && app.globalData && app.globalData.scanParams) {
          var scanParams = app.globalData.scanParams;
          if (scanParams.device && scanParams.port) {
            this.oplist = "".concat(_Config.Config.baseUrl, "/ap3200/").concat(scanParams.device, "/").concat(scanParams.port);
            // 清除全局参数，防止重复使用
            app.globalData.scanParams = null;
          } else {
            this.oplist = '';
          }
        } else {
          this.oplist = '';
        }
      }
    } else {
      this.oplist = '';
    }
    console.log('Login页面最终获取到的扫码参数:', this.oplist);
    this.isGetMobile = !uni.getStorageSync("JeepGetPhone");
    this.getLoginCode();

    // 检查是否是从App.vue跳转过来需要处理特殊情况
    if (options && options.reason) {
      this.debugReason = options.reason; // 设置调试信息
      console.log('检测到reason参数:', options.reason);
      if (options.reason === 'phone_auth') {
        console.log('需要重新进行手机号授权，当前oplist:', this.oplist);
        // 延迟执行，确保页面完全加载
        setTimeout(function () {
          _this2.triggerPhoneAuth();
        }, 500);
      } else if (options.reason === 'follow_wechat') {
        console.log('需要关注公众号，当前oplist:', this.oplist);
        // 这种情况通常会在后续的登录过程中自动处理
      }
    }
  },

  methods: {
    getLoginCode: function getLoginCode(callback) {
      var that = this;
      console.log('开始获取微信登录code');
      uni.login({
        success: function success(res) {
          console.log('获取微信登录code成功:', res.code);
          that.loginCode = res.code;
          if (callback && typeof callback === 'function') {
            callback(res.code);
          }
        },
        fail: function fail(err) {
          console.error('获取微信登录code失败:', err);
          uni.showToast({
            title: '获取登录信息失败，请重试',
            icon: 'none',
            duration: 2000
          });
          if (callback && typeof callback === 'function') {
            callback(null);
          }
        }
      });
    },
    handleOpenPrivacyContract: function handleOpenPrivacyContract() {
      this.isXieyi = true;
      uni.openPrivacyContract({});
    },
    login: function login() {
      if (!this.isXieyi) {
        return this.$toast.fail("请先阅读协议");
      }

      // 防止重复点击
      if (this.submitToggle) {
        return;
      }
      var data = {
        code: this.loginCode
      };
      this.doLogin(data);
    },
    // 处理一键登录按钮点击
    handleOneKeyLogin: function handleOneKeyLogin() {
      var _this3 = this;
      console.log('=== 一键登录按钮被点击 ===');
      console.log('当前隐私政策勾选状态:', this.isXieyi);

      // 防止重复点击
      if (this.submitToggle) {
        console.log('正在登录中，防止重复点击');
        return;
      }

      // 记录用户操作
      this.logUserAction('click_one_key_login', {
        isPrivacyAgreed: this.isXieyi
      });

      // 如果已勾选隐私政策，不需要处理，因为按钮已设置open-type="getPhoneNumber"会直接触发授权
      if (this.isXieyi) {
        console.log('隐私政策已勾选，按钮将直接触发手机号授权');
        // 确保有有效的loginCode
        if (!this.loginCode) {
          console.log('没有loginCode，重新获取');
          this.getLoginCode();
        }
        // 不需要额外处理，微信会自动触发getPhoneNumber事件
        return;
      }

      // 未勾选隐私政策，显示确认弹窗
      console.log('用户未勾选隐私政策，显示确认弹窗');
      uni.showModal({
        title: '隐私政策确认',
        content: '使用一键登录需要您同意《隐私政策》，是否同意并继续？',
        confirmText: '同意',
        cancelText: '取消',
        confirmColor: '#FF752B',
        success: function success(modalRes) {
          if (modalRes.confirm) {
            console.log('用户同意隐私政策，自动勾选并触发手机号授权');

            // 记录用户同意操作
            _this3.logUserAction('agree_privacy_policy_modal', {
              method: 'modal_confirm',
              autoChecked: true
            });

            // 自动勾选隐私政策
            _this3.isXieyi = true;

            // 给用户一个视觉反馈，显示已勾选
            uni.showToast({
              title: '已同意隐私政策',
              icon: 'success',
              duration: 1000
            });

            // 延迟一点时间再触发手机号授权，让用户看到勾选状态
            setTimeout(function () {
              // 确保有有效的loginCode
              if (!_this3.loginCode) {
                console.log('没有loginCode，重新获取');
                _this3.getLoginCode(function (newCode) {
                  if (newCode) {
                    _this3.triggerPhoneAuth();
                  }
                });
              } else {
                _this3.triggerPhoneAuth();
              }
            }, 500);
          } else {
            console.log('用户取消同意隐私政策');

            // 记录用户取消操作
            _this3.logUserAction('cancel_privacy_policy_modal', {
              method: 'modal_cancel'
            });

            // 用户取消，不执行任何操作
          }
        },

        fail: function fail(err) {
          console.error('显示隐私政策确认弹窗失败:', err);
          // 降级使用Toast提示
          uni.showToast({
            title: '请先勾选同意隐私政策',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },
    // 触发手机号授权
    triggerPhoneAuth: function triggerPhoneAuth() {
      console.log('=== 触发手机号授权 ===');
      console.log('当前loginCode:', this.loginCode);
      console.log('当前隐私政策状态:', this.isXieyi);

      // 检查必要的条件
      if (!this.loginCode) {
        console.error('没有loginCode，无法进行手机号授权');
        uni.showToast({
          title: '登录信息获取失败，请重试',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      if (!this.isXieyi) {
        console.error('用户未同意隐私政策');
        uni.showToast({
          title: '请先同意隐私政策',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 显示手机号授权按钮
      this.showPhoneAuthBtn = true;

      // 显示提示信息
      uni.showToast({
        title: '请点击按钮授权手机号',
        icon: 'none',
        duration: 2000
      });
      console.log('已切换到手机号授权按钮，等待用户点击');
    },
    onGetPhoneNumber: function onGetPhoneNumber(e) {
      console.log('=== 手机号授权回调 ===');
      console.log('手机号授权事件:', e);
      console.log('当前隐私政策状态:', this.isXieyi);

      // 记录授权结果
      this.logUserAction('phone_auth_callback', {
        authResult: e.detail.errMsg,
        isPrivacyAgreed: this.isXieyi
      });

      // 如果隐私政策未勾选，这种情况不应该发生，但做个保护
      if (!this.isXieyi) {
        console.warn('手机号授权触发但隐私政策未勾选，这是异常情况');
        uni.showToast({
          title: '请先同意隐私政策',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 直接处理手机号授权结果
      this.processPhoneNumberAuth(e);
    },
    // 处理手机号授权的核心逻辑
    processPhoneNumberAuth: function processPhoneNumberAuth(e) {
      // 防止重复点击
      if (this.submitToggle) {
        console.log('正在登录中，防止重复点击');
        return;
      }
      console.log('=== 处理手机号授权结果 ===');
      console.log('授权事件详情:', e);
      console.log('授权结果:', e.detail.errMsg);
      console.log('授权代码:', e.detail.code);

      // 检查是否获取到手机号授权
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        console.log('获取手机号失败:', e.detail.errMsg);

        // 重置按钮状态，允许用户重试
        this.showPhoneAuthBtn = false;

        // 根据不同的错误类型显示不同的提示
        var errorMessage = '获取手机号失败，请重试';
        if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
          errorMessage = '您拒绝了手机号授权，请重新尝试';
        } else if (e.detail.errMsg === 'getPhoneNumber:fail') {
          errorMessage = '手机号授权失败，请检查网络后重试';
        }
        console.log('显示错误提示:', errorMessage);
        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
        return;
      }
      console.log('手机号授权成功，开始登录');

      // 检查是否有授权代码
      if (!e.detail.code) {
        console.error('授权成功但没有获取到code');
        this.showPhoneAuthBtn = false;
        uni.showToast({
          title: '授权信息获取失败，请重试',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      // 重置按钮状态
      this.showPhoneAuthBtn = false;
      var data = {
        code: this.loginCode,
        mcode: e.detail.code
      };
      console.log('准备登录，数据:', data);
      this.doLogin(data);
    },
    doLogin: function doLogin(data) {
      var _this4 = this;
      // 设置加载状态，防止重复点击
      this.submitToggle = true;
      console.log('开始登录，设置加载状态');

      // 提取设备ID参数
      var deviceId = '';

      // 如果有扫码参数，尝试提取设备ID
      if (this.oplist && this.oplist !== 'undefined') {
        // 尝试使用正则表达式提取设备ID
        var regex = /\/([A-Fa-f0-9]{8})\/([A-Fa-f0-9]{2})(?:\/|$)/;
        var match = this.oplist.match(regex);
        if (match && match.length >= 3) {
          // 使用正则表达式匹配结果
          deviceId = match[1];
          console.log('从扫码参数提取设备ID:', deviceId);
        } else {
          // 使用原来的字符串截取方法
          deviceId = this.oplist.substring(this.oplist.length - 11, this.oplist.length - 3);
          console.log('从字符串截取提取设备ID:', deviceId);
        }
      }

      // 注释：设备ID已经在onLoad时从URL参数提取并构造到oplist中，不需要再从全局数据获取

      // 将设备ID添加到登录参数中（只有存在时才添加）
      if (deviceId) {
        data.deviceId = deviceId;
        console.log('添加设备ID到登录参数:', deviceId);
      } else {
        console.log('没有设备ID，不添加到登录参数');
      }
      console.log('登录参数:', data);

      // 调用登录接口
      this.$base.request('wxlogin/applet', 'POST', data).then(function (res) {
        res = res.data || {};
        if (res.code == 200) {
          console.log('登录成功:', res.data);
          uni.setStorageSync('user', res.data);
          uni.setStorageSync('userId', res.data.id);
          uni.setStorageSync('userToken', res.data.token);
          uni.setStorageSync('phone', res.data.phone);
          uni.setStorageSync('Name', res.data.realName);
          uni.setStorageSync('poster', res.data.poster);
          uni.setStorageSync('adunit', res.data.adunit);
          var logolist = "logolist_" + uni.getStorageSync("userId");
          uni.setStorageSync(logolist, "");
          var usertype = 'type_' + uni.getStorageSync('userId');
          uni.setStorageSync(usertype, res.data.scan);
          if (res.data.posterList) {
            var posterList = res.data.posterList;
            for (var i = 0; i < posterList.length; i++) {
              posterList[i].img = _Config.Config.imgapi + posterList[i].img;
            }
            uni.setStorageSync('posterList', posterList);
          } else {
            var posterList = [];
            uni.setStorageSync('posterList', posterList);
          }

          // 获取配置信息
          _this4.getConfig();
          _this4.guangao();
          _this4.logoshow();
        } else if (res.code == 599) {
          console.log('接口返回599状态码，需要特殊处理:', res);
          // 重置加载状态
          _this4.submitToggle = false;
          // 处理599状态码的错误提示和后续操作
          _this4.handle599Error(res);
        } else if (res.code == 598) {
          console.log('需要重新获取手机号授权');
          // 重置加载状态
          _this4.submitToggle = false;
          // 重新获取登录code，成功后触发手机号授权
          _this4.getLoginCode(function (newCode) {
            if (newCode) {
              console.log('重新获取登录code成功，触发手机号授权');
              _this4.triggerPhoneAuth();
            } else {
              console.error('重新获取登录code失败');
              uni.showToast({
                title: '获取登录信息失败，请重试',
                icon: 'none',
                duration: 2000
              });
            }
          });
        } else {
          console.log('登录失败，code:', res.code, 'msg:', res.msg);
          // 登录失败，重置状态
          _this4.submitToggle = false;
          _this4.getLoginCode();
          // 使用弹窗显示错误信息
          _this4.showLoginErrorDialog(res);
        }
      }).catch(function (err) {
        console.error('登录请求失败:', err);
        // 请求失败，重置状态
        _this4.submitToggle = false;
        // 使用弹窗显示网络错误信息
        _this4.showNetworkErrorDialog(err);
      });
    },
    logoshow: function logoshow() {
      var _this5 = this;
      var userToken = uni.getStorageSync('userToken');
      if (!userToken) {
        // 如果没有token，重置加载状态
        this.submitToggle = false;
        return;
      }
      var _this = this;
      var logolist = 'logolist_' + uni.getStorageSync('userId');
      uni.removeStorageSync(logolist);
      _this.$base.request('wechat/phone', 'GET').then(function (res) {
        if (res.data.code == 200) {
          var list = res.data.data;
          if (list.logo) {
            list.logo = _this5.$imgUrl + list.logo.replace("/tabBarimg", "");
            list.loshow = 1;
            uni.setStorageSync(logolist, list);
          }
        } else {
          _this.$toast.fail(res.data.msg);
        }
        // 完成所有登录后续处理，执行跳转
        _this5.loginOk();
      }).catch(function (err) {
        console.error('获取logo失败:', err);
        // 即使获取logo失败，也要执行跳转
        _this5.loginOk();
      });
    },
    loginOk: function loginOk() {
      console.log('登录完成，准备跳转页面');

      // 重置加载状态
      this.submitToggle = false;

      // 清除所有相关的处理标记
      uni.removeStorageSync('HANDLING_TOKEN_EXPIRED');
      uni.removeStorageSync('SHOWING_EXPIRED_DIALOG');

      // 检查是否已经跳转过
      if (this.hasRedirected) {
        console.log('已经跳转过，跳过重复跳转');
        return;
      }

      // 检查是否已经被App.vue自动跳转
      var appAutoRedirected = uni.getStorageSync('APP_AUTO_REDIRECTED');
      if (appAutoRedirected) {
        console.log('检测到App.vue已经自动跳转，清除标记并跳过登录页面跳转');
        uni.removeStorageSync('APP_AUTO_REDIRECTED');
        this.hasRedirected = true;
        return;
      }

      // 优先检查是否有重定向页面信息（来自token过期的页面）
      var redirectPageInfo = uni.getStorageSync('REDIRECT_PAGE_INFO');
      if (redirectPageInfo && (redirectPageInfo.source === 'poweron_token_expired' || redirectPageInfo.source === 'index_token_expired' || redirectPageInfo.source === 'api_token_expired')) {
        console.log('检测到来自token过期页面的重定向信息:', redirectPageInfo);
        this.handleRedirectToPage(redirectPageInfo);
        return;
      }

      // 处理扫码参数并跳转到充电页面
      if (this.oplist && this.oplist !== 'undefined') {
        console.log('登录页面处理扫码参数并跳转:', this.oplist);

        // 设置跳转标记
        this.hasRedirected = true;

        // 尝试使用正则表达式提取设备ID和端口号
        var regex = /\/([A-Fa-f0-9]{8})\/([A-Fa-f0-9]{2})(?:\/|$)/;
        var match = this.oplist.match(regex);
        var device = '';
        var port = '';
        if (match && match.length >= 3) {
          // 使用正则表达式匹配结果
          device = match[1];
          port = match[2];
          console.log('正则匹配结果:', device, port);
        } else {
          // 使用原来的字符串截取方法
          device = this.oplist.substring(this.oplist.length - 11, this.oplist.length - 3);
          port = this.oplist.substring(this.oplist.length - 2, this.oplist.length);
          console.log('字符串截取结果:', device, port);
        }

        // 处理端口号
        var processedPort = '';
        if (port.toUpperCase() !== 'FF') {
          try {
            processedPort = this.pad(parseInt(port, 16).toString(10), 2);
          } catch (e) {
            console.error('端口号转换错误:', e);
            processedPort = port;
          }
        } else {
          processedPort = port;
        }

        // 确保有有效的设备ID和端口号
        if (device && processedPort) {
          console.log('登录成功，准备跳转到充电页面:', device, processedPort);
          console.log('页面参数option:', this.option);

          // 检查页面来源，判断跳转方式
          var returnType = this.option && this.option.returnType;
          var fromPage = this.option && this.option.from;
          console.log('returnType:', returnType, 'fromPage:', fromPage);

          // 判断是否是从扫码直接进入登录页面（新用户扫码场景）
          var isDirectScanLogin = !returnType || returnType === 'charge' || fromPage === 'scan';
          if (isDirectScanLogin) {
            console.log('扫码登录场景，直接跳转到充电页面');

            // 直接跳转到充电页面，不经过index页面
            uni.redirectTo({
              url: '/pageone/poweron/poweron?device=' + device + '&port=' + processedPort + '&cmd=0&from=1',
              success: function success() {
                console.log('扫码登录后直接跳转到充电页面成功');
              },
              fail: function fail(err) {
                console.error('跳转到充电页面失败:', err);
              }
            });
          } else {
            console.log('从充电页面跳转到登录页面的场景，返回到原页面');
            // 使用navigateBack返回到原页面，原页面会自动刷新数据
            uni.navigateBack({
              delta: 1,
              success: function success() {
                console.log('成功返回到充电页面');
              },
              fail: function fail(err) {
                console.error('返回失败，使用redirectTo跳转:', err);
                // 如果返回失败，则使用redirectTo跳转
                uni.redirectTo({
                  url: '/pageone/poweron/poweron?device=' + device + '&port=' + processedPort + '&cmd=0&from=1'
                });
              }
            });
          }
          return; // 已经跳转，不执行后续代码
        }
      }

      // 如果没有扫码参数或参数无效，则使用之前的页面路径
      var PrevLoginPath = uni.getStorageSync("PrevLoginPath");
      console.log('使用之前的页面路径:', PrevLoginPath);

      // 设置跳转标记
      this.hasRedirected = true;

      // 处理特殊页面的跳转
      if (!PrevLoginPath || PrevLoginPath.indexOf("pages/index/index") >= 0) {
        console.log('跳转到首页');
        uni.switchTab({
          url: "/pages/index/index"
        });
        return;
      }
      if (PrevLoginPath && PrevLoginPath.indexOf("pages/smcharging/smcharging") >= 0) {
        console.log('跳转到扫码充电页面');
        uni.switchTab({
          url: "/pages/smcharging/smcharging"
        });
        return;
      }
      if (!PrevLoginPath || PrevLoginPath.indexOf("pages/chargingzhong/chargingzhong") >= 0) {
        console.log('跳转到充电中页面');
        uni.switchTab({
          url: "/pages/chargingzhong/chargingzhong"
        });
        return;
      }

      // 如果是其他页面，直接跳转
      if (PrevLoginPath) {
        console.log('跳转到指定页面:', PrevLoginPath);
        uni.redirectTo({
          url: PrevLoginPath
        });
      } else {
        // 默认跳转到首页
        console.log('默认跳转到扫码充电页面');
        uni.switchTab({
          url: "/pages/smcharging/smcharging"
        });
      }
    },
    // 处理重定向到指定页面
    handleRedirectToPage: function handleRedirectToPage(redirectPageInfo) {
      console.log('处理重定向到页面:', redirectPageInfo);

      // 设置跳转标记
      this.hasRedirected = true;

      // 清除重定向信息
      uni.removeStorageSync('REDIRECT_PAGE_INFO');

      // 根据页面类型选择不同的跳转方式
      if (redirectPageInfo.path === '/pages/index/index') {
        // index页面使用switchTab
        console.log('重定向到index页面，使用switchTab');
        uni.switchTab({
          url: "/pages/index/index",
          success: function success() {
            console.log('成功重定向到index页面');
          },
          fail: function fail(err) {
            console.error('重定向到index页面失败:', err);
          }
        });
      } else {
        // 其他页面使用redirectTo重新创建
        var targetUrl = redirectPageInfo.path;
        var params = redirectPageInfo.params;

        // 添加页面参数
        if (params && Object.keys(params).length > 0) {
          var paramStr = Object.keys(params).map(function (key) {
            return "".concat(key, "=").concat(encodeURIComponent(params[key]));
          }).join('&');
          targetUrl += "?".concat(paramStr);
        }
        console.log('重新创建目标页面:', targetUrl);

        // 使用redirectTo重新创建页面，确保是全新的页面实例
        uni.redirectTo({
          url: targetUrl,
          success: function success() {
            console.log('成功重定向到目标页面:', targetUrl);
          },
          fail: function fail(err) {
            console.error('重定向失败:', err);
            // 降级处理：跳转到首页
            uni.switchTab({
              url: "/pages/index/index"
            });
          }
        });
      }
    },
    // show(){
    // 	uni.login({
    // 		provider:'weixin',
    // 		onlyAuthorize:true,
    // 		success: res1=>{
    // 			var appid=uni.getAccountInfoSync().miniProgram.appId
    // 			let data = {
    // 				appid: appid,
    // 				code: res1.code
    // 			}
    // 			this.$base.request('wxlogin/minicallback', 'POST', data)
    // 				.then(res => {
    // 					if (res.data.code == '200') {
    // 						uni.setStorageSync('user', res.data.data)
    // 						uni.setStorageSync('userId', res.data.data.id);
    // 						uni.setStorageSync('userToken', res.data.data.token)
    // 						uni.setStorageSync('phone', res.data.data.phone)
    // 						uni.setStorageSync('Name', res.data.data.realName)
    // 						uni.setStorageSync('poster', res.data.data.poster)
    // 						uni.setStorageSync('adunit', res.data.data.adunit)
    // 						var logolist = "logolist_" + uni.getStorageSync("userId")
    // 						uni.setStorageSync(logolist, "")
    // 						var usertype = 'type_' + uni.getStorageSync('userId')
    // 						uni.setStorageSync(usertype, res.data.data.scan)
    // 						if (res.data.data.posterList) {
    // 							var posterList = res.data.data.posterList
    // 							for (var i = 0; i < posterList.length; i++) {
    // 								posterList[i].img = Config.imgapi + posterList[i].img
    // 							}
    // 							uni.setStorageSync('posterList', posterList)
    // 						} else {
    // 							var posterList = []
    // 							uni.setStorageSync('posterList', posterList)
    // 						}
    // 						this.getConfig()
    // 						this.guangao()
    // 						var _this = this
    // 						if(_this.oplist!='undefined'){
    // 							var device=_this.oplist.substring(_this.oplist.length-11,_this.oplist.length-3);
    // 							var port =_this.oplist.substring(_this.oplist.length-2,_this.oplist.length);
    // 							let ss = '';
    // 							if (port != 'ff' && port != 'FF') {
    // 								ss=_this.pad(parseInt(port,16).toString(10),2);
    // 							} else {
    // 								ss = port;
    // 							};
    // 							uni.redirectTo({
    // 								url:'/pageone/poweron/poweron?device=' + device + '&port=' + ss +
    // 									'&cmd=' + 0 + '&from=' + 1,
    // 							});
    // 						}else{
    // 							uni.switchTab({
    // 								url: '../smcharging/smcharging'
    // 							})
    // 						}
    // 					} else {
    // 						Toast.fail(res.data.msg)
    // 					}
    // 				}).catch(err => {
    // 				})
    // 		}
    // 	})
    // },
    pad: function pad(num, n) {
      var len = num.toString().length;
      while (len < n) {
        num = "0" + num;
        len++;
      }
      return num;
    },
    mbid: function mbid() {
      var moban = new Object();
      var appid = uni.getAccountInfoSync().miniProgram.appId;
      var data = {
        appid: appid
      };
      this.$base.request('wechat/template', 'GET', data).then(function (res) {
        if (res.data.code == '200') {
          var arr = res.data.data;
          for (var i = 0; i < arr.length; i++) {
            if (arr[i].types == 1) {
              moban.kaishi = arr[i].templateCode;
            } else if (arr[i].types == 2) {
              moban.wancheng = arr[i].templateCode;
            } else if (arr[i].types == 3) {
              moban.yichang = arr[i].templateCode;
            } else if (arr[i].types == 7) {
              moban.chongzhi = arr[i].templateCode;
            } else if (arr[i].types == 8) {
              moban.tuikuan = arr[i].templateCode;
            }
          }
          uni.setStorageSync('moban', moban);
        } else {
          _toast.default.fail(res.data.msg);
        }
      }).catch(function (err) {});
    },
    //获取商业化个人屏蔽配置
    getConfig: function getConfig() {
      var data = {
        token: uni.getStorageSync("userToken")
      };
      this.$base.request("com/getConfig", "GET", data).then(function (res) {
        if (res.data.code == "200") {
          uni.setStorageSync("reassuring", res.data.data.reassuring);
          uni.setStorageSync("postervip", res.data.data.poster);
        } else {
          // this.$toast.fail(res.data.msg)
        }
      }).catch(function (err) {});
    },
    //获取广告配置
    guangao: function guangao() {
      var data = {
        projectId: uni.getStorageSync("user").projectId
      };
      this.$base.request("poster/getPosterConfig", "POST", data).then(function (res) {
        if (res.data.code == "200") {
          uni.setStorageSync("point", res.data.data);
        } else {
          uni.setStorageSync("point", 0);
          // this.$toast.fail(res.data.msg)
        }
      }).catch(function (err) {});
    },
    // 处理599状态码错误
    handle599Error: function handle599Error(responseData) {
      console.log('=== 开始处理599状态码错误 ===');
      console.log('响应数据结构:', JSON.stringify(responseData, null, 2));
      console.log('当前时间:', new Date().toLocaleString());

      // 记录用户信息（用于调试）
      var userInfo = {
        userId: uni.getStorageSync('userId') || 'unknown',
        phone: uni.getStorageSync('phone') || 'unknown',
        deviceId: this.extractDeviceId(),
        oplist: this.oplist
      };
      console.log('用户信息:', userInfo);

      // 获取错误提示信息，优先使用msg字段
      var errorMessage = '';
      if (responseData && responseData.msg) {
        errorMessage = responseData.msg;
        console.log('使用responseData.msg作为错误信息');
      } else if (responseData && responseData.data && responseData.data.msg) {
        errorMessage = responseData.data.msg;
        console.log('使用responseData.data.msg作为错误信息');
      } else {
        errorMessage = '请扫描设备二维码进行充电';
        console.log('使用默认错误信息');
      }
      console.log('最终错误提示信息:', errorMessage);

      // 记录599错误处理操作
      this.logUserAction('handle_599_error', {
        errorMessage: errorMessage,
        hasWechatImg: !!(responseData && responseData.data && responseData.data.wechatImg),
        timestamp: new Date().toISOString()
      });

      // 显示纯信息提示弹窗
      console.log('显示599状态码信息提示弹窗');
      this.show599InfoPopup(responseData, errorMessage);
      console.log('=== 599状态码错误处理完成 ===');
    },
    // 显示网络错误弹窗
    showNetworkErrorDialog: function showNetworkErrorDialog(error) {
      var _this6 = this;
      console.log('=== 显示网络错误弹窗 ===');
      console.log('网络错误:', error);
      var errorMessage = '网络连接失败，请检查网络后重试';
      var errorTitle = '网络错误';

      // 根据错误类型设置不同的提示信息
      if (error) {
        if (error.errMsg && error.errMsg.includes('timeout')) {
          errorMessage = '请求超时，请检查网络连接后重试';
          errorTitle = '连接超时';
        } else if (error.errMsg && error.errMsg.includes('fail')) {
          errorMessage = '网络请求失败，请检查网络设置';
          errorTitle = '请求失败';
        } else if (error.statusCode) {
          errorMessage = "\u670D\u52A1\u5668\u54CD\u5E94\u5F02\u5E38 (".concat(error.statusCode, ")\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
          errorTitle = '服务器错误';
        }
      }
      console.log('网络错误标题:', errorTitle);
      console.log('网络错误信息:', errorMessage);

      // 记录网络错误日志
      this.logUserAction('show_network_error_dialog', {
        error: (error === null || error === void 0 ? void 0 : error.errMsg) || 'unknown_network_error',
        statusCode: (error === null || error === void 0 ? void 0 : error.statusCode) || null,
        message: errorMessage,
        title: errorTitle
      });

      // 使用uni.showModal显示网络错误弹窗
      uni.showModal({
        title: errorTitle,
        content: errorMessage,
        showCancel: true,
        cancelText: '取消',
        confirmText: '重试',
        confirmColor: '#FF752B',
        success: function success(modalRes) {
          if (modalRes.confirm) {
            console.log('用户选择重试网络请求');
            // 用户点击重试，重新尝试登录
            _this6.retryLogin();
          } else if (modalRes.cancel) {
            console.log('用户取消网络重试');
            // 记录用户取消操作
            _this6.logUserAction('cancel_network_retry', {
              error: (error === null || error === void 0 ? void 0 : error.errMsg) || 'unknown_network_error',
              message: errorMessage
            });
          }
        },
        fail: function fail(err) {
          console.error('显示网络错误弹窗失败:', err);
          // 如果弹窗显示失败，降级使用Toast
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        }
      });
      console.log('=== 网络错误弹窗显示完成 ===');
    },
    // 显示登录错误弹窗
    showLoginErrorDialog: function showLoginErrorDialog(responseData) {
      var _this7 = this;
      console.log('=== 显示登录错误弹窗 ===');
      console.log('响应数据:', responseData);

      // 获取错误信息
      var errorMessage = '';
      var errorTitle = '登录提示';
      if (responseData && responseData.msg) {
        errorMessage = responseData.msg;
      } else if (responseData && responseData.data && responseData.data.msg) {
        errorMessage = responseData.data.msg;
      } else {
        errorMessage = '登录失败，请重试';
      }

      // 根据不同的code值设置不同的标题和默认信息
      if (responseData && responseData.code) {
        switch (responseData.code) {
          case 400:
            errorTitle = '请求错误';
            if (!errorMessage || errorMessage === '登录失败，请重试') {
              errorMessage = '请求参数有误，请重新尝试';
            }
            break;
          case 401:
            errorTitle = '授权失败';
            if (!errorMessage || errorMessage === '登录失败，请重试') {
              errorMessage = '授权验证失败，请重新登录';
            }
            break;
          case 403:
            errorTitle = '访问被拒绝';
            if (!errorMessage || errorMessage === '登录失败，请重试') {
              errorMessage = '您没有访问权限，请联系管理员';
            }
            break;
          case 404:
            errorTitle = '服务未找到';
            if (!errorMessage || errorMessage === '登录失败，请重试') {
              errorMessage = '登录服务暂时不可用，请稍后重试';
            }
            break;
          case 500:
            errorTitle = '服务器错误';
            if (!errorMessage || errorMessage === '登录失败，请重试') {
              errorMessage = '服务器内部错误，请稍后重试';
            }
            break;
          case 502:
            errorTitle = '网关错误';
            if (!errorMessage || errorMessage === '登录失败，请重试') {
              errorMessage = '服务器网关错误，请稍后重试';
            }
            break;
          case 503:
            errorTitle = '服务不可用';
            if (!errorMessage || errorMessage === '登录失败，请重试') {
              errorMessage = '服务暂时不可用，请稍后重试';
            }
            break;
          default:
            errorTitle = '登录提示';
            // 保持原有的错误信息
            break;
        }
      }
      console.log('错误标题:', errorTitle);
      console.log('错误信息:', errorMessage);

      // 记录错误日志
      this.logUserAction('show_login_error_dialog', {
        code: (responseData === null || responseData === void 0 ? void 0 : responseData.code) || 'unknown',
        message: errorMessage,
        title: errorTitle
      });

      // 使用uni.showModal显示错误弹窗
      uni.showModal({
        title: errorTitle,
        content: errorMessage,
        showCancel: true,
        cancelText: '取消',
        confirmText: '重试',
        confirmColor: '#FF752B',
        success: function success(modalRes) {
          if (modalRes.confirm) {
            console.log('用户选择重试登录');
            // 用户点击重试，重新尝试登录
            _this7.retryLogin();
          } else if (modalRes.cancel) {
            console.log('用户取消重试');
            // 记录用户取消操作
            _this7.logUserAction('cancel_login_retry', {
              code: (responseData === null || responseData === void 0 ? void 0 : responseData.code) || 'unknown',
              message: errorMessage
            });
          }
        },
        fail: function fail(err) {
          console.error('显示错误弹窗失败:', err);
          // 如果弹窗显示失败，降级使用Toast
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        }
      });
      console.log('=== 登录错误弹窗显示完成 ===');
    },
    // 显示599状态码信息弹窗
    show599InfoPopup: function show599InfoPopup(responseData, errorMessage) {
      console.log('=== 显示599状态码信息弹窗 ===');
      console.log('错误信息:', errorMessage);

      // 处理公众号二维码（如果有）
      if (responseData && responseData.data && responseData.data.wechatImg) {
        this.qrCodeUrl = _Config.Config.imgapi + responseData.data.wechatImg;
        console.log('设置公众号二维码URL:', this.qrCodeUrl);
      } else {
        this.qrCodeUrl = '';
        console.log('没有公众号二维码信息');
      }

      // 显示弹窗
      this.showFollowGuide = true;
      console.log('显示599信息弹窗');

      // 记录弹窗显示操作
      this.logUserAction('show_599_info_popup', {
        hasQrCode: !!this.qrCodeUrl,
        errorMessage: errorMessage
      });
      console.log('=== 599信息弹窗显示完成 ===');
    },
    // 记录用户操作日志
    logUserAction: function logUserAction(action) {
      var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var logData = _objectSpread({
        action: action,
        timestamp: new Date().toISOString(),
        userId: uni.getStorageSync('userId') || 'guest',
        deviceId: this.extractDeviceId()
      }, data);
      console.log('用户操作日志:', logData);

      // 这里可以添加发送日志到服务器的逻辑
      // this.$base.request('log/user-action', 'POST', logData).catch(err => {
      //     console.error('发送用户操作日志失败:', err);
      // });
    },
    // 重试登录
    retryLogin: function retryLogin() {
      console.log('=== 用户触发重试登录 ===');
      console.log('重试时间:', new Date().toLocaleString());

      // 重新获取登录code
      this.getLoginCode();
      // 重置相关状态
      this.submitToggle = false;
      this.showFollowGuide = false;

      // 显示重试提示
      uni.showToast({
        title: '正在重新登录...',
        icon: 'loading',
        duration: 1000
      });
      console.log('重试登录状态重置完成');
    },
    // 提取设备ID的辅助方法
    extractDeviceId: function extractDeviceId() {
      if (!this.oplist || this.oplist === 'undefined') {
        return null;
      }
      try {
        // 尝试使用正则表达式提取设备ID
        var regex = /\/([A-Fa-f0-9]{8})\/([A-Fa-f0-9]{2})(?:\/|$)/;
        var match = this.oplist.match(regex);
        if (match && match.length >= 3) {
          return match[1];
        } else {
          // 使用原来的字符串截取方法
          return this.oplist.substring(this.oplist.length - 11, this.oplist.length - 3);
        }
      } catch (error) {
        console.error('提取设备ID失败:', error);
        return null;
      }
    },
    // 遮罩层点击处理（禁用点击遮罩关闭弹窗）
    onOverlayTap: function onOverlayTap() {
      // 599状态码弹窗不允许用户关闭，不做任何操作
      console.log('点击了遮罩层，599信息弹窗不允许关闭');
      this.logUserAction('attempt_close_599_popup', {
        method: 'overlay_tap',
        timestamp: new Date().toISOString()
      });
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 81:
/*!*****************************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/微信图片视频/备份代码/66_Chargingpile_applet_2.0/pages/login/login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true& ***!
  \*****************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_b237504c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true& */ 82);
/* harmony import */ var _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_b237504c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_b237504c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_b237504c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_b237504c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_4_24_2024072208_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_b237504c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 82:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/微信图片视频/备份代码/66_Chargingpile_applet_2.0/pages/login/login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[75,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map