
page {
	height: 100%;
	background-color: #242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-78b58982::-webkit-scrollbar {
  display: none;
}
.main.data-v-78b58982 {
  width: 100%;
  overflow-x: hidden;
  background-color: #242225;
  height: 100vh;
}
.main .hand.data-v-78b58982 {
  text-size-adjust: 100% !important;
  -webkit-text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
  width: 100%;
  background-color: #242225;
  font-size: 30rpx;
  padding: 20rpx 0;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
  color: #ffffff;
  position: relative;
}
.main .hand image.data-v-78b58982 {
  width: 22rpx;
  height: 39rpx;
  position: absolute;
  left: 31rpx;
  top: 22rpx;
}
.main.data-v-78b58982 .van-list__finished-text {
  margin-top: 100rpx;
  padding-bottom: 200rpx;
}
.main.data-v-78b58982 .van-hairline--top-bottom::after {
  border-width: 0;
}
.main.data-v-78b58982 .van-cell {
  padding: 0rpx;
  width: 650rpx;
  background: #343136;
  border-radius: 24rpx;
  margin: 0 auto;
  box-sizing: border-box;
  margin-bottom: 36rpx;
}
.main.data-v-78b58982 .van-cell:not(:last-child)::after {
  border-bottom: 0rpx;
}
.main.data-v-78b58982 .van-notice-bar {
  width: 650rpx;
  height: 70rpx;
  background: #343136;
  border-radius: 24rpx;
  padding: 0 24rpx;
  margin: 0 auto;
  margin-top: 30rpx;
}
.main .show.data-v-78b58982 {
  width: 100%;
  padding: 50rpx;
  box-sizing: border-box;
  height: 100vh;
}
.main .show .cdjl-0.data-v-78b58982 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  background: #343136;
  height: 100rpx;
  align-items: center;
  justify-content: space-around;
  border-radius: 18rpx;
}
.main .show .cdjl-0.data-v-78b58982 .uni-date-x--border {
  width: 100%;
  box-sizing: border-box;
  border: 0rpx !important;
}
.main .show .cdjl-0.data-v-78b58982 .uni-date-x {
  width: 100%;
  display: flex;
  box-sizing: border-box;
  background: #343136;
  align-items: center;
  padding: 0rpx !important;
  justify-content: space-between !important;
  border-radius: 18rpx;
}
.main .show .cdjl-0.data-v-78b58982 .uni-date__x-input {
  height: 60rpx;
  width: 260rpx;
  border-radius: 10rpx;
  border: 2rpx solid #ccc;
  font-size: 30rpx;
  line-height: 60rpx;
  color: #bbbbbb;
  text-align: center;
}
.main .show .map.data-v-78b58982 {
  padding: 24rpx;
  width: 650rpx;
  background: #343136;
  border-radius: 24rpx;
  margin: 0 auto;
  box-sizing: border-box;
  margin-top: 36rpx;
  box-sizing: border-box;
  position: relative;
}
.main .show .map .one.data-v-78b58982 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  align-items: center;
}
.main .show .map .one .oneleft.data-v-78b58982 {
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 42rpx;
}
.main .show .map .one .oneright.data-v-78b58982 {
  font-size: 22rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
  line-height: 30rpx;
}
.main .show .map .two .twofirst.data-v-78b58982 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.main .show .map .two .twofirst .twofirstleft.data-v-78b58982 {
  font-size: 22rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
  line-height: 30rpx;
}
.main .show .map .two .twofirst .twomap.data-v-78b58982 {
  width: 83%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.main .show .map .two .twofirst .twofirstright.data-v-78b58982 {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFA02E;
  line-height: 30rpx;
}
.main .show .map .two .twofirst .twofirstright image.data-v-78b58982 {
  width: 20rpx;
  height: 24rpx;
  margin-right: 6rpx;
}
.main .show .map .three.data-v-78b58982 {
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
}
.main .show .map .three .span.data-v-78b58982 {
  width: 138rpx;
  height: 50rpx;
  border-radius: 0rpx 24rpx 0rpx 24rpx;
  text-align: center;
  line-height: 50rpx;
}
.main .show .map .three .color1.data-v-78b58982 {
  background: #EC651C;
}
.main .show .map .three .color2.data-v-78b58982 {
  background: #FFA02E;
}
.main .show .map .three .color3.data-v-78b58982 {
  background: #A6A3A2;
}
.main .dibu.data-v-78b58982 {
  position: fixed;
  width: 100%;
  box-sizing: border-box;
  padding: 52rpx;
  bottom: 52rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.main .dibu .dibuone.data-v-78b58982 {
  width: 300rpx;
  height: 100rpx;
  background: linear-gradient(180deg, #FFE7BF 0%, #FDC894 100%);
  border-radius: 36rpx;
  font-size: 34rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #EC651C;
  line-height: 100rpx;
  text-align: center;
  letter-spacing: 2rpx;
}
.main .dibu .dibutwo.data-v-78b58982 {
  width: 300rpx;
  height: 100rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  border-radius: 36rpx;
  font-size: 34rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 100rpx;
  text-align: center;
  letter-spacing: 2rpx;
}
.main .kf.data-v-78b58982 .van-popup {
  border-radius: 36rpx;
  overflow-x: hidden;
}
.main .kf .kfmap.data-v-78b58982 {
  background-color: #ffffff;
  width: 544rpx;
  position: relative;
  padding-bottom: 50rpx;
  padding-top: 58rpx;
  box-sizing: border-box;
}
.main .kf .kfmap .one.data-v-78b58982 {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}
.main .kf .kfmap .one image.data-v-78b58982 {
  width: 29rpx;
  height: 29rpx;
}
.main .kf .kfmap .two.data-v-78b58982 {
  text-align: center;
  font-size: 34rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #111111;
  line-height: 48rpx;
}
.main .kf .kfmap .three.data-v-78b58982 {
  margin-top: 44rpx;
  text-align: center;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #3A4044;
  line-height: 42rpx;
}
.main .kf .kfmap .four.data-v-78b58982 {
  display: flex;
  padding: 0 54rpx;
  justify-content: center;
  align-items: center;
  margin-top: 70rpx;
}
.main .kf .kfmap .four .fourfirst.data-v-78b58982 {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 700;
  color: #3A4044;
  line-height: 48rpx;
}
.main .kf .kfmap .four .fourfirst image.data-v-78b58982 {
  width: 30rpx;
  height: 30rpx;
  margin-right: 5rpx;
}
.main .kf .kfmap .four .foursecond.data-v-78b58982 {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #EC651C;
  line-height: 48rpx;
}
.main .kf .kfmap .four .foursecond image.data-v-78b58982 {
  width: 35rpx;
  height: 29rpx;
  margin-right: 5rpx;
}
.main .kf .kfmap .four .fourthired.data-v-78b58982 {
  width: 1rpx;
  height: 55rpx;
  background: #EEEEEE;
  margin: 0 48rpx;
}
.main .kf .kfmap .five.data-v-78b58982 {
  text-align: center;
}
.main .kf .kfmap .five image.data-v-78b58982 {
  width: 424rpx;
  height: 424rpx;
  background: #D4D4D4;
  border-radius: 10rpx;
}
.main .kf .kfmap .six.data-v-78b58982 {
  text-align: center;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #EC651C;
  line-height: 49rpx;
  margin-top: 50rpx;
}

