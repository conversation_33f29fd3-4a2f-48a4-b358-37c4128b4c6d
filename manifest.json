{
    "name" : "66_Chargingpile_applet",
    "appid" : "__UNI__10D8741",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {},
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {},
            /* SDK配置 */
            "sdkConfigs" : {}
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx361c693866e9672c",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "postcss" : false,
            "minified" : false,
            "newFeature" : true,
            "bigPackageSizeSupport" : true,
            "useCompilerModule" : false,
            "userConfirmedUseCompilerModuleSwitch" : false
        },
        "usingComponents" : true,
        "libVersion" : "2.32.3",
        //开启分包优化
        "optimization" : {
            "subPackages" : true
        },
        // "plugins" : {
        //     "routePlan" : {
        //         "version" : "1.0.10",
        //         "provider" : "wx76a9a06e5b4e693e"
        //     }
        // },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "位置信息效果展示"
            }
        },
        "requiredPrivateInfos" : [ "getLocation" ],
        // "lazyCodeLoading": "requiredComponents"
        "cloudfunctionRoot" : "cloudfunctions/"
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "h5" : {
        "title" : "智能充电桩",
        "template" : "model.html",
        "router" : {
            "base" : "/sixsix/",
            "mode" : "hash"
        },
        "devServer" : {
            "https" : false
        },
        // "publicPath" : "https://cdn.haokuaichong.cn/sixsix",
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "HG5BZ-GXNEX-XZ64X-7CUQR-CSZWH-MKBVK"
                }
            }
        }
    }
}
