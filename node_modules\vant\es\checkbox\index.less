@import '../style/var';

.van-checkbox {
  display: flex;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  user-select: none;

  &--disabled {
    cursor: not-allowed;
  }

  &--label-disabled {
    cursor: default;
  }

  &--horizontal {
    margin-right: @padding-sm;
  }

  &__icon {
    flex: none;
    height: 1em;
    font-size: @checkbox-size;
    line-height: 1em;
    cursor: pointer;

    .van-icon {
      display: block;
      box-sizing: border-box;
      width: 1.25em;
      height: 1.25em;
      color: transparent;
      font-size: 0.8em;
      line-height: 1.25;
      text-align: center;
      border: 1px solid @checkbox-border-color;
      transition-duration: @checkbox-transition-duration;
      transition-property: color, border-color, background-color;
    }

    &--round {
      .van-icon {
        border-radius: 100%;
      }
    }

    &--checked {
      .van-icon {
        color: @white;
        background-color: @checkbox-checked-icon-color;
        border-color: @checkbox-checked-icon-color;
      }
    }

    &--disabled {
      cursor: not-allowed;

      .van-icon {
        background-color: @checkbox-disabled-background-color;
        border-color: @checkbox-disabled-icon-color;
      }
    }

    &--disabled&--checked {
      .van-icon {
        color: @checkbox-disabled-icon-color;
      }
    }
  }

  &__label {
    margin-left: @checkbox-label-margin;
    color: @checkbox-label-color;
    line-height: @checkbox-size;

    &--left {
      margin: 0 @checkbox-label-margin 0 0;
    }

    &--disabled {
      color: @checkbox-disabled-label-color;
    }
  }
}
