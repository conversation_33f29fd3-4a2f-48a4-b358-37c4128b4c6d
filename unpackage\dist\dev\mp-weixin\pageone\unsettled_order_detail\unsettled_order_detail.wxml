<view class="main data-v-daccf9ce"><view class="hand data-v-daccf9ce"><image src="../../static/tabBarimg/dbleft.png" data-event-opts="{{[['tap',[['blackbtn',['$event']]]]]}}" bindtap="__e" class="data-v-daccf9ce"></image></view><view class="show data-v-daccf9ce"><van-notice-bar vue-id="7ed13de6-1" scrollable="{{true}}" left-icon="volume-o" color="#FFFFFF" data-com-type="wx" class="data-v-daccf9ce" bind:__l="__l" vue-slots="{{['default']}}">温馨提示：如遇超期未结算订单 由于网络原因未结算，可手动点击【结束充电】按钮结算</van-notice-bar><view class="showtap data-v-daccf9ce"><image src="../../static/tabBarimg/cdshow.png" class="data-v-daccf9ce"></image></view></view><view class="showfoot data-v-daccf9ce"><view class="sffirst data-v-daccf9ce"><view class="sfone data-v-daccf9ce"><view hidden="{{!(ctype==0)}}" class="sfoneleft data-v-daccf9ce">{{"充电桩编号："+showlist.code}}</view><view hidden="{{!(ctype==1)}}" class="sfoneleft data-v-daccf9ce">{{"充电柜编号："+showlist.code}}</view><view hidden="{{!(closetype==0)}}" class="sfoneright data-v-daccf9ce">充电中</view><view hidden="{{!(closetype==1)}}" class="sfoneright data-v-daccf9ce">未结算</view></view><view class="sftwo data-v-daccf9ce">{{"充电小区："+showlist.projectName}}</view><view class="sftwo data-v-daccf9ce">{{"充电区名："+showlist.areaName}}</view><view class="sftwo data-v-daccf9ce">{{"充电端口："+showlist.port}}</view><view hidden="{{!(ctype==0)}}" class="sftwo data-v-daccf9ce">{{"充电桩编号："+showlist.code}}</view><view hidden="{{!(ctype==1)}}" class="sftwo data-v-daccf9ce">{{"充电柜编号："+showlist.code}}</view><view class="sftwo data-v-daccf9ce">{{"充电功率："+showlist.maxPower}}</view><view class="sftwo data-v-daccf9ce">{{"起始时间："+showlist.startTime}}</view><view data-event-opts="{{[['tap',[['ptbtn',['$event']]]]]}}" hidden="{{!(ctype==1&&showlist.password!='订单异常无法显示密码'&&showlist.password!='扫码临时需点击开门显示密码')}}" class="{{['data-v-daccf9ce',ptype=='0'?'sftwo':'sftwo1']}}" bindtap="__e">{{"开柜密码："+showlist.password}}</view></view><view data-event-opts="{{[['tap',[['close',[3]]]]]}}" hidden="{{!(ctype==0)}}" class="sfthree data-v-daccf9ce" bindtap="__e">结束充电</view><view hidden="{{!(ctype==1)}}" class="sffour data-v-daccf9ce"><view hidden="{{!(closetype==0)}}" class="sffourone data-v-daccf9ce"><view data-event-opts="{{[['tap',[['close',[0]]]]]}}" class="sfright data-v-daccf9ce" bindtap="__e">打开柜门</view></view><view hidden="{{!(closetype==1)}}" class="sffourtwo data-v-daccf9ce"><view data-event-opts="{{[['tap',[['close',[0]]]]]}}" class="sfright data-v-daccf9ce" bindtap="__e">打开柜门</view></view></view></view><van-toast vue-id="7ed13de6-2" id="van-toast" data-com-type="wx" class="data-v-daccf9ce" bind:__l="__l"></van-toast><van-dialog vue-id="7ed13de6-3" id="van-dialog" data-com-type="wx" class="data-v-daccf9ce" bind:__l="__l"></van-dialog></view>