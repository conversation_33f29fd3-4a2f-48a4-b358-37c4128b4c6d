
page {
	height: 100%;
	background-color: #242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-4ec66f39 {
  width: 100%;
  overflow-x: hidden;
}
.main .one.data-v-4ec66f39 {
  width: 100%;
  box-sizing: border-box;
  padding: 0 50rpx;
  margin-top: 81rpx;
}
.main .one .onetap.data-v-4ec66f39 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.main .one .onetap .onetapmont.data-v-4ec66f39 {
  width: 300rpx;
  height: 120rpx;
  background: #FFDFCE;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.main .one .onetap .onetapmont .onetapmonttxt.data-v-4ec66f39 {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242225;
}
.main .one .onetap .onetapmont image.data-v-4ec66f39 {
  margin-right: 24rpx;
}
.main .tannei1.data-v-4ec66f39 {
  width: 480rpx;
  background-color: #FFFFFF;
}
.main .tannei1 .tnhand.data-v-4ec66f39 {
  width: 100%;
  height: 64rpx;
  background-color: #4B98ED;
  align-items: center;
}
.main .tannei1 .tnhand .tntxt.data-v-4ec66f39 {
  font-size: 28rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  color: #ffffff;
  line-height: 64rpx;
  text-align: center;
  position: relative;
}
.main .tannei1 .tnhand image.data-v-4ec66f39 {
  width: 25rpx;
  height: 25rpx;
  position: absolute;
  top: 19rpx;
  right: 18rpx;
}
.main .tannei1 .tnfoot.data-v-4ec66f39 {
  width: 100%;
  margin-top: 24rpx;
  margin-bottom: 27rpx;
}
.main .tannei1 .tnfoot .tfshang.data-v-4ec66f39 {
  font-size: 24rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  color: #333333;
}
.main .tannei1 .tnfoot .tfshang1.data-v-4ec66f39 {
  font-size: 24rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  color: #F078CE;
  margin-top: 70rpx;
}
.main .tannei1 .tnfoot .tfxia.data-v-4ec66f39 {
  font-size: 28rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  color: #333333;
  line-height: 65rpx;
}
.main .tannei1 .tnfoot .tfxia1.data-v-4ec66f39 {
  font-size: 28rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  color: #F078CE;
  line-height: 65rpx;
}
.main .tannei1 .tnfoot .wxcodeimg.data-v-4ec66f39 {
  width: 260rpx;
  height: 260rpx;
  margin: 47rpx auto;
}
.main .tannei1 .tnfoot .wxcodeimg image.data-v-4ec66f39 {
  width: 260rpx;
  height: 260rpx;
}
.main .tannei1 .tnfoot .wxtxt.data-v-4ec66f39 {
  width: 100%;
  box-sizing: border-box;
  padding: 0 45rpx;
  font-size: 28rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: left;
  color: #333333;
  line-height: 50rpx;
}

