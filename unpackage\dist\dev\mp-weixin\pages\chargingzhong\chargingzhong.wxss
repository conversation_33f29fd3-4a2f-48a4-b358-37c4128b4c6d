
page {
	height: 100%;
	background-image: linear-gradient(#472E23 2%, #242225 28%, #242225 70%);
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-40ddfa5a {
  width: 100%;
  overflow-x: hidden;
  margin-bottom: 140rpx;
}
.main .foothand.data-v-40ddfa5a {
  width: 90%;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  border-radius: 24rpx;
  padding: 10rpx 24rpx;
  margin: 0 auto;
  box-sizing: border-box;
  margin-top: 30rpx;
  color: #fff;
}
.main .gztan.data-v-40ddfa5a {
  width: 650rpx;
  background-color: #FFFFFF;
  text-align: center;
  padding: 70rpx 0;
}
.main .gztan .gztanimg.data-v-40ddfa5a {
  width: 350rpx;
  height: 350rpx;
  margin: 0 auto;
}
.main .gztan .gztanimg ._img.data-v-40ddfa5a {
  width: 350rpx;
  height: 350rpx;
}
.main .gztan .gztantxt.data-v-40ddfa5a {
  margin-top: 30rpx;
  font-size: 20rpx;
}
.main.data-v-40ddfa5a .van-notice-bar {
  width: 650rpx;
  height: 70rpx;
  background: #343136;
  border-radius: 24rpx;
  padding: 0 24rpx;
  margin: 0 auto;
  margin-top: 30rpx;
}
.main .hand.data-v-40ddfa5a {
  width: 100%;
  margin-top: 90rpx;
}
.main .show.data-v-40ddfa5a {
  margin: 0 auto;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  width: 650rpx;
  box-sizing: border-box;
}
.main .show .showhand.data-v-40ddfa5a {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  padding: 30rpx 26rpx;
  background-color: #3C393E;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
}
.main .show .showhand .hleft.data-v-40ddfa5a {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
.main .show .showhand .hright.data-v-40ddfa5a {
  font-size: 26rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFA02E;
}
.main .show .showfoot.data-v-40ddfa5a {
  width: 100%;
  box-sizing: border-box;
  padding: 13rpx 0rpx 24rpx 26rpx;
  border-radius: 0rpx 0rpx 24rpx 24rpx;
  background: #343136;
}
.main .show .showfoot .fleft.data-v-40ddfa5a {
  font-size: 26rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
  margin-bottom: 10rpx;
}
.main .show .showfoot .fleft .fleftson.data-v-40ddfa5a {
  padding: 0rpx 14rpx;
  color: #333;
  background-color: #fff;
  border-radius: 5rpx;
  margin-left: 30rpx;
}
.main .notap.data-v-40ddfa5a {
  width: 245rpx;
  margin: 0 auto;
  text-align: center;
  margin-top: 300rpx;
  font-size: 26rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #6E7071;
}
.main .notap image.data-v-40ddfa5a {
  width: 245rpx;
  height: 176rpx;
  margin-bottom: 36rpx;
}
.main .kf.data-v-40ddfa5a .van-popup {
  border-radius: 36rpx;
  overflow-x: hidden;
}
.main .kf .kfmap.data-v-40ddfa5a {
  background-color: #ffffff;
  width: 544rpx;
  position: relative;
  padding-bottom: 50rpx;
  padding-top: 58rpx;
  box-sizing: border-box;
}
.main .kf .kfmap .one.data-v-40ddfa5a {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}
.main .kf .kfmap .one image.data-v-40ddfa5a {
  width: 29rpx;
  height: 29rpx;
}
.main .kf .kfmap .two.data-v-40ddfa5a {
  text-align: center;
  font-size: 34rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #111111;
  line-height: 48rpx;
}
.main .kf .kfmap .three.data-v-40ddfa5a {
  margin-top: 44rpx;
  text-align: center;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #3A4044;
  line-height: 42rpx;
}
.main .kf .kfmap .four.data-v-40ddfa5a {
  display: flex;
  padding: 0 54rpx;
  justify-content: center;
  align-items: center;
  margin-top: 70rpx;
}
.main .kf .kfmap .four .fourfirst.data-v-40ddfa5a {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 700;
  color: #3A4044;
  line-height: 48rpx;
}
.main .kf .kfmap .four .fourfirst image.data-v-40ddfa5a {
  width: 30rpx;
  height: 30rpx;
  margin-right: 5rpx;
}
.main .kf .kfmap .four .foursecond.data-v-40ddfa5a {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 700;
  color: #EC651C;
  line-height: 48rpx;
}
.main .kf .kfmap .four .foursecond image.data-v-40ddfa5a {
  width: 35rpx;
  height: 29rpx;
  margin-right: 5rpx;
}
.main .kf .kfmap .four .fourthired.data-v-40ddfa5a {
  width: 1rpx;
  height: 55rpx;
  background: #EEEEEE;
  margin: 0 48rpx;
}
.main .kf .kfmap .five.data-v-40ddfa5a {
  text-align: center;
}
.main .kf .kfmap .five image.data-v-40ddfa5a {
  width: 424rpx;
  height: 424rpx;
  background: #D4D4D4;
  border-radius: 10rpx;
}
.main .kf .kfmap .six.data-v-40ddfa5a {
  text-align: center;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #EC651C;
  line-height: 49rpx;
  margin-top: 50rpx;
}

