
page{
	height: 100%;
	background-color:#242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-fdafec4e {
  width: 100%;
  overflow-x: hidden;
  background-color: #242225;
}
.main .kshow.data-v-fdafec4e {
  width: 686rpx;
  margin: 0 auto;
  background-color: #242225;
  margin-top: 51rpx;
}
.main .kshow.data-v-fdafec4e .van-cell:after {
  border-bottom: 0rpx;
  border-bottom-color: transparent;
}
.main .kshow .kone.data-v-fdafec4e {
  width: 100%;
  margin-bottom: 40rpx;
  background: #343136;
  border-radius: 24rpx;
  box-sizing: border-box;
  padding: 29rpx 36rpx;
}
.main .kshow .kone input.data-v-fdafec4e {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
}
.main .kshow .ktwo.data-v-fdafec4e {
  width: 100%;
  box-sizing: border-box;
  background-color: #343136;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
}
.main .kshow .ktwo.data-v-fdafec4e .van-collapse-item {
  background-color: #343136;
  border-radius: 24rpx;
}
.main .kshow .ktwo.data-v-fdafec4e .tclass {
  font-size: 28rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  color: #999999;
}
.main .kshow .ktwo.data-v-fdafec4e .tclass1 {
  font-size: 28rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 700;
  color: #D0D0D0;
}
.main .kshow .ktwo.data-v-fdafec4e .van-cell {
  padding: 29rpx 36rpx;
  box-sizing: border-box;
  background: #343136;
  border-radius: 24rpx;
}
.main .kshow .ktwo.data-v-fdafec4e .van-radio__label {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  margin-left: 90rpx;
}
.main .kshow .ktwo.data-v-fdafec4e .van-radio__icon {
  margin-left: 80rpx;
}
.main .kshow .ktwo.data-v-fdafec4e .van-collapse-item__content {
  background-color: #343136;
  border-radius: 24rpx;
}
.main .kshow .ktwo.data-v-fdafec4e .van-hairline--top-bottom::after {
  border-width: 0rpx;
}
.main .kshow .ktwo.data-v-fdafec4e .van-radio {
  height: 70rpx;
  align-items: center;
}
.main .kshow .ktwo.data-v-fdafec4e .van-collapse-item__wrapper {
  margin-bottom: 10rpx;
}
.main .kshow .ktwo.data-v-fdafec4e .van-cell:not(:last-child)::after {
  border-bottom: 0rpx;
}
.main .kshow .ktwo.data-v-fdafec4e .van-radio__icon--checked {
  background-color: #EC651C;
  border-color: #EC651C;
}
.main .kshow .kthree .kthreefoot.data-v-fdafec4e {
  width: 100%;
  box-sizing: border-box;
}
.main .kshow .kthree .kthreefoot.data-v-fdafec4e .van-cell {
  background-color: #343136;
  height: 236rpx;
  border-radius: 24rpx;
}
.main .kshow .kthree .kthreefoot.data-v-fdafec4e .van-field__control {
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
.main .kshow .kfour.data-v-fdafec4e {
  width: 100%;
  box-sizing: border-box;
  margin-top: 35rpx;
}
.main .kshow .kfour .kfourtxt.data-v-fdafec4e {
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  margin-bottom: 24rpx;
}
.main .kshow .kfour .kfourtxt .kfourbud.data-v-fdafec4e {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
}
.main .kshow .kfour .add-img-box.data-v-fdafec4e {
  display: flex;
  width: 750rpx;
  flex-direction: row;
  flex-wrap: wrap;
}
.main .kshow .kfour .add-img-item.data-v-fdafec4e {
  width: 128rpx;
  height: 128rpx;
  border-radius: 16rpx;
  position: relative;
  padding: 9rpx 0;
  margin-right: 20rpx;
}
.main .kshow .kfour .add-img-camera.data-v-fdafec4e {
  flex: 1;
}
.main .kshow .kfour .add-img.data-v-fdafec4e {
  width: 128rpx;
  height: 128rpx;
  border-radius: 16rpx;
}
.main .kshow .kfour .add-img-del.data-v-fdafec4e {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  right: -14rpx;
  top: -6rpx;
  border-radius: 20rpx;
}
.main .kshow .kfour .address-time.data-v-fdafec4e {
  width: 484rpx;
  height: 88rpx;
  background-color: whitesmoke;
  opacity: 1;
  border-radius: 24rpx;
  text-align: center;
  font-size: 35rpx;
  font-weight: 500;
  color: #333333;
}
.main .kshow .kfour .line.data-v-fdafec4e {
  width: 750rpx;
  height: 1px;
  -webkit-transform: scaleY(0.3);
          transform: scaleY(0.3);
  background-color: rgba(0, 0, 0, 0.5);
}
.main .kshow .kbtn.data-v-fdafec4e {
  width: 686rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  border-radius: 36rpx;
  font-size: 32rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
  padding: 20rpx 0;
  margin: 0 auto;
  margin-top: 59rpx;
  color: #ffffff;
}

