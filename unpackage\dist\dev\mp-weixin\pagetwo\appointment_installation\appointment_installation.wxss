
page{
	height: 100%;
	background-color:#242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-66ea7fc2 {
  width: 100%;
  overflow-x: hidden;
}
.main .show.data-v-66ea7fc2 {
  width: 100%;
  box-sizing: border-box;
  padding: 50rpx;
}
.main .show.data-v-66ea7fc2 .van-picker__toolbar .van-picker__confirm {
  border: 0rpx solid;
  background-color: #FFFFFF;
}
.main .show.data-v-66ea7fc2 .van-picker__toolbar .van-picker__cancel {
  border: 0rpx solid;
  background-color: #FFFFFF;
}
.main .show .sone.data-v-66ea7fc2 {
  width: 100%;
  display: flex;
  box-sizing: border-box;
  padding: 20rpx 10rpx;
  align-items: center;
  background-color: #242225;
}
.main .show .sone.data-v-66ea7fc2 .van-cell:after {
  border-bottom: 0rpx;
  border-bottom-color: transparent;
}
.main .show .sone.data-v-66ea7fc2 .tclass {
  font-size: 28rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  color: #999999;
}
.main .show .sone.data-v-66ea7fc2 .van-collapse-item {
  border-radius: 24rpx;
  background-color: #343136;
}
.main .show .sone.data-v-66ea7fc2 .van-collapse-item__content {
  background-color: #343136;
  border-radius: 24rpx;
}
.main .show .sone.data-v-66ea7fc2 .tclass1 {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
}
.main .show .sone.data-v-66ea7fc2 .van-hairline--top-bottom::after {
  border-width: 0;
}
.main .show .sone.data-v-66ea7fc2 .van-cell:not(:last-child)::after {
  border-bottom: 0rpx;
}
.main .show .sone.data-v-66ea7fc2 .van-cell {
  width: 100%;
  background: #343136;
  padding: 26rpx 29rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  border-radius: 24rpx;
}
.main .show .sone.data-v-66ea7fc2 .van-radio__label {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
}
.main .show .sone.data-v-66ea7fc2 .van-collapse {
  border-radius: 24rpx;
  width: 464rpx;
}
.main .show .sone.data-v-66ea7fc2 .van-radio__icon /deep/.van-icon {
  width: 20rpx;
  height: 20rpx;
  border: 1rpx solid #cccccc;
}
.main .show .sone.data-v-66ea7fc2 .van-radio {
  height: 70rpx;
  align-items: center;
}
.main .show .sone .soneleft.data-v-66ea7fc2 {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  width: 42%;
}
.main .show .sone .soneleft1.data-v-66ea7fc2 {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  width: 38%;
}
.main .show .sone .soneright.data-v-66ea7fc2 {
  width: 100%;
  background: #343136;
  border-radius: 24rpx;
  padding: 26rpx 29rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
.main .show .sone input.data-v-66ea7fc2 {
  width: 100%;
  background: #343136;
  border-radius: 24rpx;
  padding: 26rpx 29rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
.main .show .sone textarea.data-v-66ea7fc2 {
  width: 100%;
  height: 180rpx;
  background: #343136;
  border-radius: 24rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
  padding: 30rpx 29rpx;
  box-sizing: border-box;
}
.main .show .sone.data-v-66ea7fc2 .van-radio__icon--checked {
  background-color: #EC651C;
  border-color: #EC651C;
}
.main .show .stwo.data-v-66ea7fc2 {
  width: 100%;
  height: 88rpx;
  border-radius: 20rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  font-size: 32rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
  line-height: 88rpx;
  margin: 0 auto;
  color: #ffffff;
  margin-top: 50rpx;
}

