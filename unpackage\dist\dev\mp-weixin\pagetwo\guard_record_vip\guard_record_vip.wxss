
page {
	height: 100%;
	background-color: #242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-d1e6ae02::-webkit-scrollbar {
  display: none;
}
.main.data-v-d1e6ae02 {
  width: 100%;
  overflow-x: hidden;
  background-color: #242225;
  height: 100vh;
}
.main .hand.data-v-d1e6ae02 {
  width: 100%;
  background-color: #242225;
  font-size: 30rpx;
  padding: 20rpx 0;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
  color: #ffffff;
  position: relative;
}
.main .hand image.data-v-d1e6ae02 {
  width: 22rpx;
  height: 39rpx;
  position: absolute;
  left: 31rpx;
  top: 22rpx;
}
.main .show.data-v-d1e6ae02 {
  width: 100%;
  padding: 50rpx;
  box-sizing: border-box;
  height: 100vh;
}
.main .show .map.data-v-d1e6ae02 {
  width: 100%;
  padding: 24rpx;
  box-sizing: border-box;
  position: relative;
  background: #343136;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
}
.main .show .map .one.data-v-d1e6ae02 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 13rpx;
  font-size: 30rpx;
  font-weight: 400;
  color: #FFFFFF;
}
.main .show .map .two .twofirst.data-v-d1e6ae02 {
  width: 100%;
  box-sizing: border-box;
  margin-top: 14rpx;
  font-size: 30rpx;
  font-weight: 400;
  color: #FFFFFF;
}
.main .show .map .two .twosecond.data-v-d1e6ae02 {
  width: 100%;
  box-sizing: border-box;
  margin-top: 14rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #D0D0D0;
}
.main .show .map .two .twosecond ._span.data-v-d1e6ae02 {
  color: #FFA02E;
}
.main .show .map .three.data-v-d1e6ae02 {
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #EC651C;
}
.main .show .map .three .span.data-v-d1e6ae02 {
  width: 138rpx;
  height: 50rpx;
  border-radius: 0rpx 24rpx 0rpx 24rpx;
  text-align: center;
  line-height: 50rpx;
}
.main .show .map .three .color1.data-v-d1e6ae02 {
  background: linear-gradient(90deg, #FFE7BF 0%, #FDC894 100%);
}
.main .dibu.data-v-d1e6ae02 {
  width: 100%;
  text-align: center;
  font-size: 35rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-top: 100rpx;
}

