@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-daccf9ce {
  width: 100%;
  overflow-x: hidden;
}
.main .qiun-charts.data-v-daccf9ce {
  width: 686rpx;
  height: 500rpx;
  background-color: #FFFFFF;
}
.main .charts.data-v-daccf9ce {
  width: 686rpx;
  height: 500rpx;
  background-color: #FFFFFF;
}
.main .hand.data-v-daccf9ce {
  width: 100%;
  background-color: #242225;
  height: 62rpx;
  font-size: 30rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
  line-height: 62rpx;
  color: #ffffff;
  position: relative;
}
.main .hand image.data-v-daccf9ce {
  width: 22rpx;
  height: 39rpx;
  position: absolute;
  left: 31rpx;
  top: 11rpx;
}
.main .show.data-v-daccf9ce {
  width: 100%;
  margin: 20rpx 0;
}
.main .show.data-v-daccf9ce .van-notice-bar {
  width: 650rpx;
  height: 70rpx;
  background: #343136;
  border-radius: 24rpx;
  padding: 0 24rpx;
  margin: 0 auto;
}
.main .show .showtap.data-v-daccf9ce {
  width: 100%;
  background-color: #242225;
  text-align: center;
  box-sizing: border-box;
  padding-right: 50rpx;
}
.main .show .showtap image.data-v-daccf9ce {
  width: 312rpx;
  height: 290rpx;
}
.main .showfoot.data-v-daccf9ce {
  width: 100%;
  box-sizing: border-box;
  padding: 0 40rpx;
}
.main .showfoot .sffirst.data-v-daccf9ce {
  background-color: #3C393E;
  border-radius: 24px;
  padding-bottom: 30rpx;
}
.main .showfoot .sffirst .sfone.data-v-daccf9ce {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 30rpx 26rpx;
  background-color: #343136;
  border-radius: 24px 24px 0 0;
}
.main .showfoot .sffirst .sfone .sfoneleft.data-v-daccf9ce {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
.main .showfoot .sffirst .sfone .sfoneright.data-v-daccf9ce {
  font-size: 26rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFA02E;
}
.main .showfoot .sffirst .sftwo.data-v-daccf9ce {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  margin-top: 13rpx;
  margin-left: 26rpx;
}
.main .showfoot .sffirst .sftwo1.data-v-daccf9ce {
  font-size: 40rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  margin-top: 13rpx;
  margin-left: 26rpx;
}
.main .showfoot .sttwo.data-v-daccf9ce {
  margin-top: 20rpx;
}
.main .showfoot .sttwo.data-v-daccf9ce .van-cell {
  padding: 10rpx 15rpx;
  font-size: 24rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  color: #666666;
}
.main .showfoot .sttwo.data-v-daccf9ce .van-collapse-item__content {
  padding: 0rpx;
}
.main .showfoot .sttwo .hmtxt.data-v-daccf9ce {
  font-size: 24rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  color: #666666;
}
.main .showfoot .sttwo .flone.data-v-daccf9ce {
  text-align: center;
  line-height: 150rpx;
  font-size: 30rpx;
}
.main .showfoot .tap.data-v-daccf9ce {
  width: 649rpx;
  height: 300rpx;
  border-radius: 16rpx;
  margin: 40rpx auto;
  margin-bottom: 50rpx;
}
.main .showfoot .tap image.data-v-daccf9ce {
  width: 649rpx;
  height: 240rpx;
  border-radius: 16rpx;
}
.main .showfoot .sfthree.data-v-daccf9ce {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  font-size: 32rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 88rpx;
  margin: 176rpx auto;
}
.main .showfoot .sffour .sffourone.data-v-daccf9ce {
  width: 100%;
  display: flex;
  justify-content: center;
}
.main .showfoot .sffour .sffourone .sfleft.data-v-daccf9ce {
  width: 45%;
  height: 88rpx;
  border-radius: 16rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  font-size: 32rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 88rpx;
  margin: 20rpx auto;
}
.main .showfoot .sffour .sffourone .sfright.data-v-daccf9ce {
  width: 95%;
  height: 88rpx;
  border-radius: 16rpx;
  background: linear-gradient(180deg, #FFE175 0%, #FF9D07 100%);
  font-size: 32rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 88rpx;
  margin: 20rpx auto;
}
.main .showfoot .sffour .sffourtwo.data-v-daccf9ce {
  width: 100%;
  display: flex;
  justify-content: center;
}
.main .showfoot .sffour .sffourtwo .sfleft.data-v-daccf9ce {
  width: 45%;
  height: 88rpx;
  border-radius: 16rpx;
  background-color: #8d8d8d;
  font-size: 32rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 88rpx;
  margin: 20rpx auto;
}
.main .showfoot .sffour .sffourtwo .sfright.data-v-daccf9ce {
  width: 95%;
  height: 88rpx;
  border-radius: 16rpx;
  background: linear-gradient(180deg, #FFE175 0%, #FF9D07 100%);
  font-size: 32rpx;
  font-family: PingFang SC Medium, PingFang SC Medium-Medium;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 88rpx;
  margin: 20rpx auto;
}

