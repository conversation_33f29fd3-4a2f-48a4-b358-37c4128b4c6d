
page{
	height: 100%;
	background-color:#242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-30fef97f {
  width: 100%;
  overflow-x: hidden;
  background-color: #242225;
}
.main .hand.data-v-30fef97f {
  text-size-adjust: 100% !important;
  -webkit-text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
  width: 100%;
  background-color: #242225;
  font-size: 30rpx;
  padding: 20rpx 0;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 700;
  text-align: center;
  color: #ffffff;
  position: relative;
}
.main .hand image.data-v-30fef97f {
  width: 22rpx;
  height: 39rpx;
  position: absolute;
  left: 31rpx;
  top: 22rpx;
}
.main .show .one.data-v-30fef97f {
  width: 100%;
  box-sizing: border-box;
  padding: 0 50rpx;
  margin-top: 81rpx;
}
.main .show .one .onetap.data-v-30fef97f {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.main .show .one .onetap .onetapmont.data-v-30fef97f {
  width: 300rpx;
  height: 120rpx;
  background: #FFDFCE;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.main .show .one .onetap .onetapmont .onetapmonttxt.data-v-30fef97f {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242225;
}
.main .show .one .onetap .onetapmont image.data-v-30fef97f {
  margin-right: 24rpx;
}

