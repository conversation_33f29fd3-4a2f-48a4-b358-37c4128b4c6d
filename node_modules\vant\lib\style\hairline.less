@import './var';
@import './mixins/hairline';

[class*='van-hairline'] {
  &::after {
    .hairline();
  }
}

.van-hairline {
  &,
  &--top,
  &--left,
  &--right,
  &--bottom,
  &--surround,
  &--top-bottom {
    position: relative;
  }

  &--top::after {
    border-top-width: @border-width-base;
  }

  &--left::after {
    border-left-width: @border-width-base;
  }

  &--right::after {
    border-right-width: @border-width-base;
  }

  &--bottom::after {
    border-bottom-width: @border-width-base;
  }

  &,
  &-unset {
    &--top-bottom::after {
      border-width: @border-width-base 0;
    }
  }

  &--surround::after {
    border-width: @border-width-base;
  }
}
