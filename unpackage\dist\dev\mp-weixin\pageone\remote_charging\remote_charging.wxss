
page{
	height: 100%;
	background-color:#242225;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main.data-v-424cf24f {
  width: 100%;
  overflow-x: hidden;
}
.main .show.data-v-424cf24f {
  width: 100%;
  margin-top: 85rpx;
  margin-bottom: 41rpx;
  box-sizing: border-box;
  padding: 10rpx 32rpx;
}
.main .show .sone.data-v-424cf24f {
  width: 100%;
  background-color: #343136;
  display: flex;
  justify-content: space-between;
  padding: 24rpx 36rpx;
  align-items: center;
  box-sizing: border-box;
  border-radius: 24rpx;
}
.main .show .sone .sonebtn.data-v-424cf24f {
  width: 180rpx;
  height: 68rpx;
  background: linear-gradient(180deg, #FFA73E 0%, #FF752B 100%);
  border-radius: 24rpx;
  line-height: 68rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  text-align: center;
}
.main .show .sone input.data-v-424cf24f {
  width: 502rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
  padding: 11rpx 0;
}
.main .show .stwo.data-v-424cf24f {
  width: 100%;
  box-sizing: border-box;
  background-color: #343136;
  padding: 40rpx 0;
  margin-top: 22rpx;
  border-radius: 24rpx;
}
.main .show .stwo .kfour.data-v-424cf24f {
  width: 100%;
  box-sizing: border-box;
  text-align: center;
}
.main .show .stwo .kfour .kfourtxt.data-v-424cf24f {
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #D0D0D0;
  margin-top: 24rpx;
}
.main .show .stwo .kfour .montbtn.data-v-424cf24f {
  width: 100rpx;
  height: 62rpx;
  border-radius: 10rpx;
  background-color: #4B98ED;
  color: #FFFFFF;
  line-height: 62rpx;
  text-align: center;
  margin: 10 auto;
}
.main .show .stwo .kfour .add-img-box.data-v-424cf24f {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.main .show .stwo .kfour .add-img-item.data-v-424cf24f {
  width: 156rpx;
  height: 156rpx;
  border-radius: 16rpx;
  position: relative;
  padding: 9rpx 0;
  margin: 0 auto;
}
.main .show .stwo .kfour .add-img-camera.data-v-424cf24f {
  flex: 1;
}
.main .show .stwo .kfour .add-img.data-v-424cf24f {
  width: 156rpx;
  height: 156rpx;
  border-radius: 16rpx;
}
.main .show .stwo .kfour .add-img-item1.data-v-424cf24f {
  width: 156rpx;
  height: 156rpx;
  border-radius: 16rpx;
  position: relative;
  padding: 9rpx 0;
  margin: 0 auto;
}
.main .show .stwo .kfour .add-img1.data-v-424cf24f {
  width: 156rpx;
  height: 156rpx;
  border-radius: 16rpx;
}
.main .show .stwo .kfour .add-img-del.data-v-424cf24f {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  right: -14rpx;
  top: -6rpx;
  border-radius: 20rpx;
}
.main .show .stwo .kfour .address-time.data-v-424cf24f {
  width: 484rpx;
  height: 88rpx;
  background-color: whitesmoke;
  opacity: 1;
  border-radius: 24rpx;
  text-align: center;
  font-size: 35rpx;
  font-weight: 500;
  color: #333333;
}
.main .show .stwo .kfour .line.data-v-424cf24f {
  width: 750rpx;
  height: 1px;
  -webkit-transform: scaleY(0.3);
          transform: scaleY(0.3);
  background-color: rgba(0, 0, 0, 0.5);
}
.main .show .sthree.data-v-424cf24f {
  font-size: 24rpx;
  font-family: PingFang SC Bold, PingFang SC Bold-Bold;
  font-weight: 500;
  color: #D0D0D0;
  line-height: 46rpx;
  margin-top: 28rpx;
}
.main .show .sthree .sthreehand.data-v-424cf24f {
  color: #FFFFFF;
  font-weight: 700;
}

