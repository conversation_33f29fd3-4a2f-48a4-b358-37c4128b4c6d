@import '../style/var';

.van-contact-list {
  box-sizing: border-box;
  height: 100%;
  padding-bottom: 80px;

  &__item {
    padding: @contact-list-item-padding;
  }

  &__item-value {
    display: flex;
    align-items: center;
    padding-right: @padding-xl;
    padding-left: @padding-xs;
  }

  &__item-tag {
    flex: none;
    margin-left: @padding-xs;
    padding-top: 0;
    padding-bottom: 0;
    line-height: 1.4em;
  }

  &__group {
    box-sizing: border-box;
    height: 100%;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
  }

  &__edit {
    font-size: @contact-list-edit-icon-size;
  }

  &__bottom {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: @contact-list-add-button-z-index;
    padding: 0 @padding-md;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: @white;
  }

  &__add {
    height: 40px;
    margin: 5px 0;
  }
}
