<view class="main data-v-4144027d"><view class="show data-v-4144027d"><view class="sone data-v-4144027d"><block wx:for="{{showlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['jumpbtn',['$0'],[[['showlist','',index]]]]]]]}}" class="btap data-v-4144027d" bindtap="__e"><view class="dbleft data-v-4144027d"><view class="dbtext data-v-4144027d">{{item.title}}</view></view><view class="dbright data-v-4144027d"><image src="../../static/tabBarimg/kright.png" class="data-v-4144027d"></image></view></view></block><uni-load-more vue-id="318663ae-1" status="{{status}}" icon-size="{{16}}" content-text="{{contentText}}" class="data-v-4144027d" bind:__l="__l"></uni-load-more></view></view></view>